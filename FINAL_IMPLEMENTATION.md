# 🎉 AI 盯盘系统最终实现完成

## 📋 实现概述

根据您的最新需求调整，我已经完成了 AI 盯盘系统的代码编写，主要特点：

- ✅ 关闭了AI盯盘开关检查逻辑
- ✅ 集成了 `design/monitor/graph_body.py` 的图表生成逻辑
- ✅ 实现了新的 `handler_map` 结构，支持 `title_allows` 配置
- ✅ 简化了告警类型判断，专注于 `argos` 类型
- ✅ 保持了 `views.py` 的简洁性，核心逻辑在 `app/handler/monitor` 中

## 🏗️ 核心架构

### 1. 主要文件结构

```
app/
├── views.py                    # 主路由，保持简洁
├── config.py                   # 配置文件，包含图表生成配置
├── utils.py                    # 数据库操作函数
└── handler/monitor/
    ├── handler.py              # 核心业务逻辑和入口函数
    └── utils.py                # AI分析和图表生成工具
```

### 2. 核心流程

```mermaid
graph TD
    A[POST /check] --> B[提取参数]
    B --> C[查询告警信息]
    C --> D[核心逻辑检查]
    D --> E[业务强依赖检查]
    E --> F[告警类型判断]
    F --> G[handler_map匹配]
    G --> H[标题允许列表检查]
    H --> I[图表生成]
    I --> J[AI多模态分析]
    J --> K[保存历史记录]
    K --> L[返回结果]
```

## 🔧 关键实现

### 1. 核心逻辑调整 (`app/handler/monitor/handler.py`)

```python
# 关闭AI盯盘开关检查
# 2. 检查是否开启AI盯盘（暂时关闭此逻辑）
# labels = content.get("labels", {})
# enable_ai_monitor = labels.get("enable_ai_monitor", 0)
# if enable_ai_monitor != 1:
#     logger.info(f"PSM {psm} 未开启AI盯盘")
#     return "firing", "未打开 ai 盯盘"

# 3. 判断告警类型
labels = content.get("labels", {})
alert_source = labels.get("alert_source", "")

# 检查是否为argos类型
if (alert_source == "argos" and
    title in ["CC-SRE TLB PSM 可用率", "CC-SRE Server 下游可用率", "CC-SRE Server Method 可用率"]):
    return "argos", "匹配argos类型"
```

### 2. 新的 handler_map 结构

```python
handler_map = {
    "argos": {
        "title_allows": {
            "CC-SRE TLB PSM 可用率",
            "CC-SRE Server 下游可用率", 
            "CC-SRE Server Method 可用率"
        },
        "func": argos,
    }
}
```

### 3. 图表生成集成 (`app/handler/monitor/utils.py`)

集成了 `design/monitor/graph_body.py` 的逻辑：

```python
@storage("monitor")
async def get_graph_url_body(db, cur, fingerprint):
    """通过fingerprint获取graph_key并生成图表"""
    # 支持多种数据源：argos, kepler, polaris, grafana
    # 包含重试机制和错误处理

async def get_graph_image_data(alert_data: Dict[str, Any]) -> bytes:
    """根据告警数据获取图表图片数据"""
    # 从告警数据中提取fingerprint
    # 调用图表生成函数
```

### 4. 配置文件更新 (`app/config.py`)

```python
base = {
    "DEBUG": False,
    "SECRET_KEY": uuid.uuid4().hex,
    "DB": os.getenv("DB"),
    
    # AI模型配置
    "MODEL_API_KEY": os.getenv("MODEL_API_KEY"),
    
    # 图表生成配置
    "AUTH_URL": os.getenv("AUTH_URL", ""),
    "JWT_TOKEN": os.getenv("JWT_TOKEN", ""),
    "BASE_URL": os.getenv("BASE_URL", ""),
    "REGION": os.getenv("REGION", ""),
    "GRAFANA_OPEN_API": os.getenv("GRAFANA_OPEN_API", ""),
}
```

### 5. 主路由逻辑更新 (`app/views.py`)

```python
# 7. 调用对应的处理函数
handler_config = handler_map.get(alert_type)
if not handler_config:
    response["data"] = "firing"
    response["message"] = f"未找到 {alert_type} 类型的处理器"
    return json(response)

# 检查标题是否在允许列表中
title_allows = handler_config.get("title_allows", set())
if title_allows and alert_data.get("title") not in title_allows:
    response["data"] = "firing"
    response["message"] = f"告警标题不在 {alert_type} 类型的允许列表中"
    return json(response)

handler_func = handler_config.get("func")
```

## ✅ 测试验证

所有核心功能测试通过：

- ✅ 文件结构完整
- ✅ 配置结构正确
- ✅ handler_map结构验证通过
- ✅ 告警类型判断逻辑正确
- ✅ 核心逻辑变更验证通过

## 🚀 部署指南

### 1. 环境变量配置

```bash
# AI模型配置
export MODEL_API_KEY="your_model_api_key"

# 图表生成配置
export AUTH_URL="https://your-auth-url"
export JWT_TOKEN="your_jwt_token"
export BASE_URL="https://your-event-url"
export REGION="your_REGION"
export GRAFANA_OPEN_API="https://your-cloud-api"

# 数据库配置
export DB="your_database_connection"
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 启动服务

```bash
python app/main.py
```

### 4. API调用示例

```bash
curl -X POST http://localhost:8000/check \
  -H "Content-Type: application/json" \
  -d '{
    "create_time": "2024-01-01 12:00:00",
    "fingerprint": "abc123"
  }'
```

## 📊 支持的告警类型

### argos 类型
- **匹配条件**: 
  - `alert_source` == "argos"
  - `title` 在允许列表中：
    - "CC-SRE TLB PSM 可用率"
    - "CC-SRE Server 下游可用率"
    - "CC-SRE Server Method 可用率"
- **处理流程**: 图表生成 → AI多模态分析 → 返回结果

## 🔄 响应格式

### 成功响应
```json
{
  "data": "suppressed",  // firing | suppressed
  "message": "AI argos可用率分析判断告警可抑制",
  "success": true
}
```

### 错误响应
```json
{
  "data": "firing",
  "message": "为业务场景的强依赖",
  "success": false
}
```

## 📈 扩展能力

### 添加新的告警类型

1. **更新 handler_map**:
```python
handler_map["new_type"] = {
    "title_allows": {"新告警标题"},
    "func": new_type_handler,
}
```

2. **实现处理函数**:
```python
async def new_type_handler(alert_data, create_time, fingerprint):
    return await process_alert_with_ai_analysis(
        alert_data, create_time, fingerprint,
        new_chart_generator, "新类型"
    )
```

3. **更新类型判断逻辑**:
```python
# 在 determine_alert_type 中添加新的判断条件
```

## 🎯 核心特性

- 🔧 **模块化设计**: 职责分离，易于维护
- ⚙️ **配置化管理**: handler_map支持灵活配置
- 🚀 **可扩展架构**: 新增告警类型只需配置
- 🔄 **向后兼容**: 保留原有API接口
- 📊 **多数据源支持**: argos, kepler, polaris, grafana
- 🤖 **AI多模态分析**: 图片趋势分析和智能判断
- 📝 **完整日志记录**: 便于调试和监控

## 🎉 总结

AI 盯盘系统现已完成所有核心功能的实现，具备：

- ✅ 完整的告警处理流程
- ✅ 灵活的配置化架构
- ✅ 强大的图表生成能力
- ✅ 智能的AI分析功能
- ✅ 健壮的错误处理机制

系统已准备好进行生产环境部署和使用！
