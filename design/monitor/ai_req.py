# 构建系统提示词
system_prompt = ""
with open(f'{base["PT_BASE_PATH"]}/xxx/user.md', "r") as f:
    system_prompt = f.read()

# 调用 AI 模型
headers = {
    "Content-Type": "application/json",
    'Authorization': f'Bearer {base["MODEL_API_KEY"]}',
}

async with httpx.AsyncClient() as client:
    response = await client.post(
        url="https://ark-cn-beijing.bytedance.net/api/v3/chat/completions",
        headers=headers,
        json={
            "model": "ep-20250715121128-kpg25",
            "messages": [
            ]
        },
        timeout=120
    )

    if response.status_code == 200:
        result = response.json()
        buf = []
        for item in result.get("choices", []):
            if item.get("message", {}).get("role") == "assistant":
                data = item["message"]["content"]
                buf.append(data)

        ai_summary = "\n".join(buf)