- 介绍: AI 盯盘
在海量噪声中做降噪，由 AI 来响应告警并识别出真的需要处理的告警

代码结构:
- app/handler/monitor.py 中存具体的处理逻辑，其中的 handler_map 就是记录的支持哪些类别，每种类别都是通过字段去查询出一个二进制的图
  - argos
- app/views.py 对应的 @ai.route('/check', methods=["POST"]) 路由，逻辑顺序:
  - 从请求参数中提取出 create_time 和 fingerprint 这是告警的唯一 ID 查询出告警的原信息，如果查询出了多个信息 data 字段返回 firing， 不在继续观察告警, message 提示有唯一 ID 关联多个告警不符合预期
  - 使用函数 get_alert_item 查询出告警的字段 psm, title, level, content, 分别是 服务名、告警标题、告警等级、告警的元信息，content 中有完整的告警字段是一字符串存储的 JSON 格式的内容，从其中可以提取出各种判断依赖的内容，可以先 json.loads(content) 以备后用
    - 如果通过 create_time 和 fingerprint 查询到的 ai_moniotr_history 结果是 fring 则直接返回 firing, 已经是需要马上升级了不需要再做其他判断了
    - 如果当前盯盘的任务超过了 30 个，那也处理不过来了，直接回 fring
    - 获取 psm 如果 psm 是业务场景的强依赖则 data 字段返回 firing, message 提示为业务场景的强依赖，但是也依然要继续用 AI 来分析图形结果
      - psm 是业务场景的强依赖, 如果返回的 f_fingerprint 长度不为 0 
        select distinct f_fingerprint from cmdb.server_core_pathway_backend_link_v2 where swd_mark = 'strong' and (service = 'xx' or to_service = 'xx')
      - 如果告警 create_time 相对现在已经持续了超过 20 分钟，并跳过 AI 图形分析
      <!-- - 关联的业务场景中的业务告警 title 在 design/monitor/sql.sql 最近 1 小时的告警中存在且 status 不等于 recover
         select title from  server_core_pathway_bs_v2 where f_fingerprint in  (select f_fingerprint from server_core_pathway_entrypoint_v2 where b_fingerprint in (select distinct f_fingerprint from cmdb.server_core_pathway_backend_link_v2 where swd_mark = 'strong' and (service = 'xx' or to_service = 'xx'))) -->
    - 「这个逻辑暂时关闭」如果 content["labels"].get("enable_ai_monitor", 0) 为 1 则继续后面的流程，否则修改 data 字段为 firing, message 改为未打开 ai 盯盘
    - 使用哪种类型的 check, 之后用 app/handler/monitor 中的 handler_map 取对应的函数去处理
      - argos: content["labels"]["alert_source"] == argos 以及 title 为 CC-SRE TLB PSM 可用率、CC-SRE Server 下游可用率、CC-SRE Server Method 可用率 中的一个
    - design/monitor/graph_body.py 中已经把各种告警类型的图片生成逻辑封装好了，feishu_card_graph_info 里面存量图标的类别，以及数据的使用方法，发起请求后就可以获得图的 body，所以直接从这里获取数据就行，可以通过 fingprint 来查询
  - 保持 views.py 的简单，逻辑处理代码尽量在 app/handler/monitor 中
- 将图片格式化成 base64 参考 design/monitor/ai_req.curl、design/monitor/ai_req.py 实现异步的 httpx 请求的方式
  - 提示词在 app/prompt/monitor/user.md 中
  - 其中有强调返回的格式，如果不满足则重试，最多重试 1 次
  - 如果都失败修改 data 字段为 firing, message 改为 AI 多模态图片趋势分析失败
- 每一次判断都使用 design/monitor/sql.sql 中的 ai_moniotr_history 来生成一次记录来回收拦下告警的收益
- 在检查告警状态时如果有任何异常状态 data 字段返回 firing, message 指为未知错误无法提供 AI 盯盘能力

- 提供配置能力，如果满足这些配置且 AI 状态为非 firing 才返回 AI 的状态，表结构在 design/monitor/sql.sql 的 ai_moniotr_block_alert 中
  - type 支持 3 种
    - type = title
      - content = 具体的告警 title
    - title = group
      - content = 具体的告警组（默认整个组全开
    - title = tg_fingerprint
      - content = title#group#fingerprint 的拼接，需要同时满足 3 个字段
  - 类似 ai_moniotr_history 支持对这个表的 list 以及对单个 id 的 post，如果 id 为 0 就是创建，其他是更新

- 以下规则返回 firing
  - 命中核心链路
  - 持续的时间超过了配置的时长 ALERT_DURATION_THRESHOLD_MINUTES，之前提供配置能力这里有阻断效果，这里的逻辑优先级要更高