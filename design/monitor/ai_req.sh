BASE64_IMAGE=$(base64 < path_to_your_image.jpeg) && curl https://ark-cn-beijing.bytedance.net/api/v3/chat/completions \
   -H "Content-Type: application/json"  \
   -H "Authorization: Bearer $ARK_API_KEY"  \
   -d @- <<EOF
   {
    "model": "doubao-seed-1-6-250615",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "image_url",
            "image_url": {
              "url": "data:image/jpeg;base64,$BASE64_IMAGE"
            }
          },
          {
            "type": "text",
            "text": "图里有什么"
          }
        ]
      }
    ],
    "max_tokens": 300
  }
EOF
