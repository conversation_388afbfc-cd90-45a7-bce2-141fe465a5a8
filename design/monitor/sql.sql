 CREATE TABLE `alertman_history` (
  `create_time` datetime NOT NULL COMMENT "",
  `fingerprint` varchar(130) NOT NULL COMMENT "",
  `alertname` varchar(1024) NOT NULL COMMENT "",
  `alert_source` varchar(64) NOT NULL COMMENT "",
  `psm` varchar(1024) NOT NULL COMMENT "",
  `level` varchar(32) NOT NULL COMMENT "",
  `title` varchar(1024) NOT NULL COMMENT "",
  `group` varchar(64) NOT NULL COMMENT "",
  `region` varchar(64) NULL DEFAULT "cn" COMMENT "",
  `duration` bigint(20) NOT NULL COMMENT "",
  `ack_time` datetime NOT NULL COMMENT "",
  `recover_time` datetime NOT NULL COMMENT "",
  `realy_event` bigint(20) NOT NULL COMMENT "",
  `version` bigint(20) NOT NULL COMMENT "",
  `updatetime` datetime NOT NULL COMMENT "",
  `content` varchar(65533) NOT NULL COMMENT "",
  `uuid` varchar(256) NULL COMMENT "",
  `status` varchar(32) NULL COMMENT "",
  `state` varchar(32) NOT NULL DEFAULT "active" COMMENT "",
  `aid` varchar(256) NULL COMMENT "",
  `income` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP

CREATE TABLE `ai_moniotr_history` (
  `create_time` datetime NOT NULL COMMENT "",
  `fingerprint` varchar(130) NOT NULL COMMENT "",
  `status` varchar(255) NOT NULL COMMENT "",
  `alertname` varchar(1024) NOT NULL COMMENT "",
  `alert_source` varchar(64) NOT NULL COMMENT "",
  `psm` varchar(1024) NOT NULL COMMENT "",
  `level` varchar(32) NOT NULL COMMENT "",
  `title` varchar(1024) NOT NULL COMMENT "",
  `group` varchar(64) NOT NULL COMMENT "",
  `content` varchar(65533) NOT NULL COMMENT "",
  `reason` varchar(65533) NOT NULL COMMENT ""
  PRIMARY KEY (`create_time`, `fingerprint`)
) ENGINE=OLAP
PRIMARY KEY(`create_time`, `fingerprint`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`fingerprint`) BUCKETS 32
PROPERTIES (
"replication_num" = "3",
"in_memory" = "false",
"enable_persistent_index" = "true",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ai_moniotr_recored` (
  `create_time` datetime NOT NULL COMMENT "告警创建时间 create_time+fingerprint 为告警唯一 ID",
  `fingerprint` varchar(130) NOT NULL COMMENT "告警指纹 create_time+fingerprint 为告警唯一 ID",
  `create_at` datetime NOT NULL COMMENT "记录创建时间",
  `url` varchar(65533) NOT NULL COMMENT "图表生成URL",
  `content` varchar(65533) NOT NULL COMMENT "图表base64内容",
  `response` varchar(65533) NOT NULL COMMENT "AI 分析回的分析内容"
) ENGINE=OLAP
PRIMARY KEY(`create_time`, `fingerprint`, `create_at`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`fingerprint`) BUCKETS 32
PROPERTIES (
"replication_num" = "3",
"in_memory" = "false",
"enable_persistent_index" = "true",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `ai_moniotr_block_alert` (
  `rule_id` varchar(120) NOT NULL COMMENT "rule ID",
  `alert_group` varchar(255) NOT NULL COMMENT "告警组",
  `block_type` varchar(255) NOT NULL COMMENT "block 的类型, 支持 title|group|tg_fingerprint",
  `enable` boolean  NOT NULL COMMENT "启用开关",
  `content` varchar(65500) NOT NULL COMMENT "block 的类型, 支持 title|group|tg_fingerprint",
  `update_at` datetime NOT NULL COMMENT "记录创建时间"
) ENGINE=OLAP
PRIMARY KEY(`rule_id`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`rule_id`) BUCKETS 32
PROPERTIES (
"replication_num" = "3",
"in_memory" = "false",
"enable_persistent_index" = "true",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `feishu_card_graph_info` (
  `create_time` datetime NULL COMMENT "",
  `fingerprint` varchar(256) NOT NULL COMMENT "",
  `source` varchar(256) NOT NULL COMMENT "",
  `vregion` varchar(256) NULL COMMENT "",
  `value` varchar(65535) NOT NULL COMMENT ""
) ENGINE=OLAP
DUPLICATE KEY(`create_time`, `fingerprint`, `source`, `vregion`, `value`)
PARTITION BY RANGE(create_time)(
)
DISTRIBUTED BY HASH(create_time)
PROPERTIES(
    "dynamic_partition.enable" = "true",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-30",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.history_partition_num" = "0",
    "replication_num" = "3"
);