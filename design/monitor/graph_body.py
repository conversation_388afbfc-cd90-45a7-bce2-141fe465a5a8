
async def get_token_jwt():
    async with httpx.AsyncClient(verify=False) as client:
        response = await client.get(
            url=base["AUTH_URL"],
            headers={"Authorization": f"Bearer {base['JWT_TOKEN']}"}
        )
        x_token_jwt = response.headers.get("X-Jwt-Token")
        return x_token_jwt

# 通过fingerprint获取graph_key
@storage("monitor")
async def get_graph_url_body(db, cur, fingerprint):
    try:
        sql = f'''SELECT source, value, vregion
FROM feishu_card_graph_info
WHERE fingerprint = "{fingerprint}"
AND create_time = (
    SELECT create_time
    FROM feishu_card_graph_info
    WHERE fingerprint = "{fingerprint}"
    ORDER BY create_time DESC
    LIMIT 1
)
LIMIT 1;'''
        await cur.execute(sql)
        print(f'[Card_Graph] Executing SQL: {sql}')
        result = await cur.fetchall()
        if len(result) == 0:
            print(
                f"[Card_Graph] No value found for fingerprint: {fingerprint}")
            return ""
        source, value, vregion = result[0][0], result[0][1], result[0][2]
        print(f"[Card_Graph] Retrieved bosun: {value}")
    except Exception as err:
        print(f'[Card_Graph] Executing SQL: {sql}')
        print(f"[Card_Graph] Database query failed for {fingerprint}")
        return ""

    retry_num = 0
    while retry_num <= 2:
        retry_num += 1
        try:
            async with httpx.AsyncClient(verify=False) as client:
                if source == 'argos':
                    print(f'[Card_Graph] argos graph')
                    data = {
                        'bosun': value,
                        'vregion': vregion
                    }
                    headers = { 
                        'Content-Type': 'application/json'
                    }
                    response = await client.post(url=f'{base["BASE_URL"]}/api/v1/kepler/generate_graph/{base["REGION"]}/0', timeout=5, headers=headers, json=data)

                elif source == 'kepler':
                    print(f'[Card_Graph] kepler graph')
                    headers = {
                        'Content-Type': 'application/json'
                    }
                    response = await client.get(url=f'{base["BASE_URL"]}/api/v1/kepler/generate_graph/{base["REGION"]}/{value}', timeout=5, headers=headers)


                elif source == 'polaris':
                    print(f'[Card_Graph] polaris graph')
                    headers = {
                        'Content-Type': 'application/json'
                    }
                    data=  {
                        "fingerprint": fingerprint,
                        'sql': value
                    }
                    response = await client.post(url=f'{base["BASE_URL"]}/api/v1/kepler/generate_graph/{base["REGION"]}/1', timeout=5, headers=headers, json=data)


                elif source == 'grafana':
                    print(f'[Card_Graph] grafana graph')

                    _token = await get_token_jwt()
                    data = {
                        "url": value,
                        "width": 500,
                        "height": 250,
                        "device_scale_factor": 0,
                        "diagnosis": True,
                    }
                    headers = {
                        'X-JWT-Token': _token,
                        'Content-Type': 'application/json'
                    }
                    print(f'#rpc: upload req {data}')
                    response = await client.post(url=f'{base["GRAFANA_OPEN_API"]}/api/v1/grafana_open_api/screenshot', timeout=10, headers=headers, json=data)
                    response.raise_for_status()
                    body = response.content
                    response.raise_for_status()
                    body = response.content
                    # 记录响应内容的长度
                    print(f"[Card_Graph] Graph generation response length: {len(body)} bytes")

        except Exception as err:
            print(f'#[Card_Graph] get_card_graph_key err: {err}')
            await asyncio.sleep(2 ** retry_num * 0.4)
    return ""
