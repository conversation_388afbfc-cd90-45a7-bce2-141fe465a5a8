# AI 盯盘系统架构重构文档

## 🎯 重构目标

将原有的硬编码告警处理逻辑重构为可配置、可扩展的架构，支持：
- 不同类型的图表生成
- 不同类型的告警分析
- 灵活的告警类型匹配规则
- 易于添加新的监控系统支持

## 🏗️ 新架构设计

### 1. 模块化组件

#### 📊 图表生成器 (`chart_generators.py`)
- **BaseChartGenerator**: 抽象基类，定义通用图表生成流程
- **SLIAvailabilityChartGenerator**: SLI可用率图表
- **LatencyChartGenerator**: 延迟图表  
- **ErrorRateChartGenerator**: 错误率图表
- **CHART_GENERATORS**: 图表生成器注册表

#### ⚙️ 告警配置 (`alert_config.py`)
- **AlertTypeConfig**: 告警类型配置类
- **ALERT_TYPE_CONFIGS**: 告警类型配置注册表
- 支持灵活的匹配条件：
  - `alert_source`: 告警源匹配
  - `title_exact`: 标题精确匹配
  - `title_contains`: 标题包含关键词
  - `labels`: 标签匹配

#### 🔧 核心处理器 (`handler.py`)
- **process_alert_with_ai_analysis**: 通用AI分析流程
- **create_alert_handler**: 告警处理器工厂
- **build_handler_map**: 动态构建处理器映射

### 2. 配置化告警类型

```python
# 当前支持的告警类型
ALERT_TYPE_CONFIGS = {
    'argos_sli': AlertTypeConfig(
        name='argos_sli',
        chart_generator=generate_sli_chart,
        analysis_type='SLI可用率',
        match_conditions={
            'alert_source': 'argos',
            'title_exact': [
                # 'CC-SRE TLB PSM 可用率',
                'CC-SRE Server 下游可用率', 
                'CC-SRE Server Method 可用率'
            ]
        }
    ),
    
    'argos_latency': AlertTypeConfig(
        name='argos_latency',
        chart_generator=generate_latency_chart,
        analysis_type='延迟',
        match_conditions={
            'alert_source': 'argos',
            'title_contains': ['延迟', 'latency', 'RT', '响应时间']
        }
    ),
    
    'argos_error_rate': AlertTypeConfig(
        name='argos_error_rate', 
        chart_generator=generate_error_rate_chart,
        analysis_type='错误率',
        match_conditions={
            'alert_source': 'argos',
            'title_contains': ['错误率', 'error rate', '失败率']
        }
    )
}
```

## 🚀 扩展新告警类型

### 步骤1: 创建图表生成器

```python
class CustomChartGenerator(BaseChartGenerator):
    def __init__(self):
        super().__init__("自定义监控", (12, 6))
    
    async def get_chart_data(self, alert_data):
        # 实现具体的数据获取逻辑
        time_points = [...]
        values = [...]
        config = {
            'threshold': 100,
            'unit': 'units',
            'subtitle': '自定义趋势',
            'ylabel': '自定义指标'
        }
        return time_points, values, config
```

### 步骤2: 注册图表生成器

```python
# 在 chart_generators.py 中添加
CHART_GENERATORS['custom_metric'] = CustomChartGenerator()

# 在 utils.py 中添加便捷函数
async def generate_custom_chart(alert_data):
    return await generate_chart_by_type(alert_data, 'custom_metric')
```

### 步骤3: 配置告警类型

```python
# 在 alert_config.py 中添加
ALERT_TYPE_CONFIGS['custom_alert'] = AlertTypeConfig(
    name='custom_alert',
    chart_generator=generate_custom_chart,
    analysis_type='自定义指标',
    match_conditions={
        'alert_source': 'custom_system',
        'title_contains': ['自定义关键词']
    }
)
```

### 步骤4: 添加处理器（可选）

```python
# 在 handler.py 的 build_handler_map 中添加
elif alert_type == "custom_alert":
    handler_map[alert_type] = custom_alert_handler
```

## 📈 架构优势

### 1. 可扩展性
- ✅ 新增告警类型只需配置，无需修改核心逻辑
- ✅ 图表生成器可独立开发和测试
- ✅ 支持不同监控系统的接入

### 2. 可维护性
- ✅ 职责分离，每个模块功能单一
- ✅ 配置化管理，减少硬编码
- ✅ 统一的错误处理和日志记录

### 3. 可测试性
- ✅ 每个组件可独立测试
- ✅ 模拟数据生成便于测试
- ✅ 配置驱动的行为便于验证

### 4. 向后兼容
- ✅ 保留原有API接口
- ✅ 渐进式迁移支持
- ✅ 现有功能不受影响

## 🔄 处理流程

```mermaid
graph TD
    A[接收告警] --> B[核心逻辑检查]
    B --> C[确定告警类型]
    C --> D[获取类型配置]
    D --> E[生成监控图表]
    E --> F[转换Base64]
    F --> G[AI多模态分析]
    G --> H[保存历史记录]
    H --> I[返回处理结果]
    
    C --> J[未知类型]
    J --> K[返回firing状态]
```

## 🛠️ 使用示例

### 添加新的监控系统支持

```python
# 1. 创建Prometheus CPU图表生成器
class PrometheusCPUChartGenerator(BaseChartGenerator):
    async def get_chart_data(self, alert_data):
        # 从Prometheus查询CPU数据
        # ...
        return time_points, cpu_values, config

# 2. 注册图表生成器
CHART_GENERATORS['prometheus_cpu'] = PrometheusCPUChartGenerator()

# 3. 配置告警类型
ALERT_TYPE_CONFIGS['prometheus_cpu'] = AlertTypeConfig(
    name='prometheus_cpu',
    chart_generator=generate_prometheus_cpu_chart,
    analysis_type='CPU使用率',
    match_conditions={
        'alert_source': 'prometheus',
        'labels': {'alertname': 'HighCPUUsage'}
    }
)
```

## 📋 下一步计划

1. **数据源集成**: 集成真实的监控数据API
2. **缓存优化**: 添加图表数据缓存机制
3. **配置管理**: 支持动态配置更新
4. **监控面板**: 开发配置管理界面
5. **性能优化**: 并行处理和异步优化

## 🎉 总结

通过这次重构，AI盯盘系统现在具备了：
- 🔧 **高度可配置**: 通过配置文件管理告警类型
- 🚀 **易于扩展**: 新增告警类型只需几行配置
- 🎯 **职责清晰**: 每个模块功能单一明确
- 🔄 **向后兼容**: 不影响现有功能使用

这为系统的长期发展和维护奠定了坚实的基础！
