请帮我完成一下的需求，直接帮我修改代码
- 告诉我哪些条件会进行 ai 盯盘的行为，触发的条件和流程是怎么样，最终效果会怎么样
- 告诉我哪些条件会进行 ai 阻断的行为，触发的条件和流程怎么样的，最终效果会怎么样
- 我现在能否通过哪个 view 接口进行快速新增 ai 抑制的条件，我需要外部进行新增，调用接口的 url 是什么，以及传入的请求体和参数是什么，并且给我解释参数的含义

# 新增按标题阻断的规则
curl -X POST "http://cc-dockerhub.byted.org/api/v1/ai-monitor/ai_block_rules/0" \
  -H "Content-Type: application/json" \
  -d '{
    "block_type": "title",
    "content": "测试告警标题",
    "enable": true,
    "alert_group": "商业化"
  }'

# 新增按告警组阻断的规则
curl -X POST "http://localhost/api/v1/ai-monitor/ai_block_rules/0" \
  -H "Content-Type: application/json" \
  -d '{
    "block_type": "group", 
    "content": "测试组",
    "enable": true,
    "alert_group": "商业化"
  }'


    curl -X POST http://cc-dockerhub.byted.org/api/v1/rca/send_rca_task \
  -d '{
    "create_time": "2025-08-27 15:09:14",
    "fingerprint": "995b44fb0199713b6b6952d404f1405e",
    "message_id": "om_x100b45debc2ff8d80f3dee51adfe482"
  }'

# 按告警标题拦截
{
    "block_type": "title",
    "content": "CC-SRE TLB PSM 可用率",
    "alert_group": "商业化"
}


# 按告警组拦截
{
    "block_type": "group", 
    "content": "商业化",
    "alert_group": "商业化"
}

# 按组合指纹拦截
{
    "block_type": "tg_fingerprint",
    "content": "CC-SRE TLB PSM 可用率#商业化#995b44fb0199713b6b6952d404f1405e",
    "alert_group": "商业化"
}