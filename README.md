# AI 盯盘系统实现完成报告

## 📋 项目概述

AI 盯盘系统是一个智能告警处理系统，通过 AI 多模态分析来识别真正需要处理的告警，在海量噪声中进行降噪。

## ✅ 已完成的功能模块

### 1. 核心路由处理 (`app/views.py`)
- ✅ 实现 `/check` POST 路由
- ✅ 告警参数验证 (create_time, fingerprint)
- ✅ 告警信息查询和验证
- ✅ 完整的错误处理和响应格式

### 2. 数据库操作 (`app/utils.py`)
- ✅ `get_alert_item()` - 查询告警信息
- ✅ `check_business_strong_dependency()` - 检查业务强依赖
- ✅ `save_ai_monitor_history()` - 保存AI监控历史
- ✅ `check_recent_business_alerts()` - 检查最近业务告警
- ✅ 数据库连接池和重试机制

### 3. 核心业务逻辑 (`app/handler/monitor/handler.py`)
- ✅ `process_alert_core_logic()` - 核心告警处理逻辑
- ✅ 业务强依赖检查
- ✅ AI盯盘开关检查
- ✅ 告警类型判断
- ✅ `determine_alert_type()` - 告警类型识别
- ✅ `validate_alert_data()` - 数据验证
- ✅ `save_monitor_history()` - 历史记录保存

### 4. AI 多模态分析 (`app/handler/monitor/utils.py`)
- ✅ `format_graph_base64()` - 图片转base64
- ✅ `get_ai_response()` - AI请求处理
- ✅ 重试机制 (最多1次重试)
- ✅ JSON响应格式验证
- ✅ 错误处理和日志记录

### 5. argos_sli 处理器 (`app/handler/monitor.py`)
- ✅ `argos_sli()` - argos_sli类型告警处理
- ✅ `generate_sli_chart()` - SLI监控图表生成
- ✅ matplotlib图表绘制
- ✅ 时间序列数据模拟
- ✅ 图表样式和格式化

### 6. AI 提示词 (`app/prompt/monitor/user.md`)
- ✅ 专业的AI分析提示词
- ✅ 明确的判断标准
- ✅ 严格的输出格式要求
- ✅ firing/suppressed状态定义

## 🔧 技术实现特点

### 架构设计
- **模块化设计**: 逻辑分离，views.py保持简洁
- **异步处理**: 全面使用async/await
- **错误处理**: 完善的异常捕获和日志记录
- **数据验证**: 多层数据验证机制

### 核心流程
1. **参数验证** → 提取create_time和fingerprint
2. **告警查询** → 获取psm, title, level, content
3. **业务检查** → 强依赖检查、AI开关检查
4. **类型判断** → 确定告警类型(argos_sli等)
5. **AI分析** → 图表生成 → base64转换 → AI请求
6. **结果处理** → 状态判断 → 历史记录 → 响应返回

### 安全特性
- SQL注入防护 (参数化查询)
- 输入验证和清理
- 错误信息脱敏
- 超时控制

## 🚀 部署和使用

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 环境配置
```bash
export MODEL_API_KEY="your_api_key_here"
```

### 3. 数据库配置
确保数据库连接配置正确，包含以下表：
- `alertman_history` - 告警历史表
- `ai_moniotr_history` - AI监控历史表
- `server_core_pathway_backend_link_v2` - 业务依赖表

### 4. 启动服务
```bash
python app/main.py
```

### 5. API调用示例
```bash
curl -X POST http://localhost:8000/check \
  -H "Content-Type: application/json" \
  -d '{
    "create_time": "2024-01-01 12:00:00",
    "fingerprint": "abc123"
  }'
```

## 📊 响应格式

### 成功响应
```json
{
  "data": "suppressed",  // firing | suppressed | recover(可以结单了)
  "message": "AI分析判断告警可抑制",
  "success": true
}
```

### 错误响应
```json
{
  "data": "firing",
  "message": "未打开 ai 盯盘",
  "success": false
}
```

## 🔍 支持的告警类型

### argos_sli
- **条件**: alert_source == "argos"
- **标题**: "CC-SRE TLB PSM 可用率", "CC-SRE Server 下游可用率", "CC-SRE Server Method 可用率"
- **处理**: 生成SLI趋势图 → AI多模态分析

## 📈 监控和日志

- 完整的操作日志记录
- AI分析历史追踪
- 错误监控和告警
- 性能指标收集

## 🎯 下一步优化建议

1. **扩展告警类型**: 添加更多告警类型处理器
2. **实时数据源**: 集成真实监控数据API
3. **缓存优化**: 添加Redis缓存提升性能
4. **监控面板**: 开发管理界面
5. **A/B测试**: 对比AI判断准确率
6. **模型优化**: 根据反馈调优AI模型

## ✨ 总结

AI 盯盘系统已完成核心功能开发，具备：
- 🔄 完整的告警处理流程
- 🤖 AI多模态图片分析
- 📊 动态图表生成
- 🛡️ 健壮的错误处理
- 📝 详细的日志记录
- 🔧 模块化架构设计

系统已准备好进行部署和测试！

## 📊 图表记录功能

系统会自动保存每次生成的图表数据到 `ai_moniotr_recored` 表中，方便调试和分析。

### 查询图表记录

```bash
# 查询保存的图表数据
curl -X POST http://localhost/api/v1/ai-monitor/chart \
  -H "Content-Type: application/json" \
  -d '{
    "create_time": "2024-01-01 12:00:00",
    "fingerprint": "abc123"
  }'
```

**响应格式:**
```json
{
  "data": {
    "url": "argos可用率图表生成",
    "content": "data:image/png;base64,iVBORw0KGgo...",
    "create_at": "2024-01-01 12:00:05"
  },
  "message": "查询图表记录成功",
  "success": true
}
```

### 图表数据用途

- 🔍 **调试分析**: 查看AI分析的原始图表数据和分析结果
- 📊 **历史追踪**: 保留告警处理时的图表快照和AI判断
- 🧪 **测试验证**: 验证图表生成和AI分析是否正确
- 📈 **趋势分析**: 对比不同时间的图表数据和AI判断结果
- 🤖 **AI分析回溯**: 查看AI的具体分析内容、置信度和推理过程

## 📋 任务管理接口

### GET /task/list - 查询任务列表

查询最近100个分析任务：

```bash
curl -X GET http://localhost/api/v1/ai-monitor/task/list
```

**响应格式:**
```json
{
  "data": [
    {
      "create_time": "2024-01-01 12:00:00",
      "fingerprint": "abc123",
      "status": "suppressed",
      "alertname": "CC-SRE TLB PSM 可用率",
      "alert_source": "argos",
      "psm": "example.service",
      "level": "warning",
      "title": "告警标题",
      "group": "sre",
      "content": "告警内容",
      "start_at": "2024-01-01 12:00:05"
    }
  ],
  "message": "查询任务列表成功",
  "success": true
}
```

### GET /task/<create_time>/<fingerprint> - 查询任务详情

查询特定任务的详细信息：

```bash
curl -X GET "http://localhost/api/v1/ai-monitor/task/2024-01-01%2012:00:00/abc123"
```

### GET /task/step/<create_time>/<fingerprint> - 查询步骤详情

查询任务的每个步骤详情，包含图表内容和AI分析结果：

```bash
curl -X GET "http://localhost/api/v1/ai-monitor/task/step/2024-01-01%2012:00:00/abc123"
```

**响应包含:**
- `content`: 图表的base64内容
- `response`: AI分析结果JSON
- `url`: 步骤描述
- `create_at`: 步骤执行时间

## 🤖 后台告警监控任务

系统内置后台任务，自动监控 `alertman_history` 表中的告警，旁路验证降噪能力。

### 监控配置

- **目标告警组**: `影像|内容生态`, `影像|商业化`
- **目标告警标题**:
  - `CC-SRE TLB PSM 可用率`
  - `CC-SRE Server 下游可用率`
  - `CC-SRE Server Method 可用率`
- **执行频率**: 每30秒检查一次
- **查询窗口**: 最近1分钟的告警数据

### 处理流程

1. **告警检索**: 从 `alertman_history` 表查询符合条件的 `firing` 状态告警
2. **条件筛选**: 过滤出目标告警组和标题的告警
3. **任务创建**: 为每个告警调用 `process_alert_check` 创建盯盘任务
4. **结果记录**: 保存到 `ai_moniotr_history` 表，包含判断理由
5. **统计更新**: 更新处理统计信息

### 监控指标

通过 `/metrics` 接口查看后台任务统计：

```bash
curl -X GET http://localhost/metrics | python3 -c "
import json, sys
data = json.load(sys.stdin)
bg_stats = data.get('background_tasks', {})
print('后台任务统计:')
print(f'  运行时间: {bg_stats.get(\"uptime_seconds\", 0)} 秒')
print(f'  处理告警数: {bg_stats.get(\"processed_alerts\", 0)}')
print(f'  创建任务数: {bg_stats.get(\"created_tasks\", 0)}')
print(f'  失败任务数: {bg_stats.get(\"failed_tasks\", 0)}')
print(f'  成功率: {bg_stats.get(\"success_rate\", \"0%\")}')
"
```

### 优势特点

- **自动化**: 无需手动触发，自动发现和处理告警
- **旁路验证**: 不影响现有告警流程，独立验证降噪效果
- **实时监控**: 30秒检查频率，快速响应新告警
- **统计分析**: 完整的处理统计，便于分析降噪效果
- **容错处理**: 异常情况下不影响主服务运行

## 🎯 判断理由记录 (reason字段)

系统会在 `reason` 字段中记录告警判断的具体标准，帮助分析和优化告警处理策略。

### 判断标准对应的reason值

- **业务强依赖**: `"业务强依赖"` - PSM为业务场景的强依赖
- **8分钟兜底逻辑**: `"8分钟兜底逻辑"` - 告警持续时间超过8分钟
- **AI分析判断**: `"AI分析判断"` - 通过AI多模态分析得出结论
- **告警类型匹配**: `"告警类型匹配"` - 匹配到已知的告警类型
- **未知告警类型**: `"未知告警类型"` - 无法识别的告警类型
- **数据格式错误**: `"数据格式错误"` - 告警数据格式异常
- **系统异常**: `"系统异常"` - 处理过程中发生异常
- **AI分析异常**: `"AI分析异常"` - AI分析过程异常

### 使用场景

```bash
# 查询不同判断理由的统计
curl -s http://localhost/api/v1/ai-monitor/task/list | \
  python3 -c "
import json, sys
from collections import Counter
data = json.load(sys.stdin)
if data['success']:
    reasons = [task.get('reason', '未设置') for task in data['data']]
    counter = Counter(reasons)
    print('判断理由统计:')
    for reason, count in counter.most_common():
        print(f'  {reason}: {count}次')
"
```

## 🔄 历史记录检查逻辑

### 新增功能

系统现在会在处理告警前检查历史记录状态，避免重复分析已确定为 `firing` 的告警。

### 处理流程

1. **告警数据解析**: 从 `alertman_history` 获取 `psm`, `title`, `level`, `content`
2. **历史状态检查**: 查询 `ai_moniotr_history` 表中的状态
   ```sql
   SELECT status FROM ai_moniotr_history
   WHERE create_time = ? AND fingerprint = ?
   ```
3. **快速返回**: 如果历史状态为 `firing`，直接返回
   ```
   return "firing", "历史记录显示已经是需要马上升级", True
   ```
4. **继续处理**: 如果历史状态为其他或不存在，执行完整的分析流程

### 优势特点

- **⚡ 性能优化**: 避免重复的AI分析计算
- **🎯 结果一致**: 保持相同告警的判断结果稳定
- **💡 业务合理**: 符合"已升级告警无需再分析"的逻辑
- **📊 资源节约**: 减少不必要的图表生成和AI调用

### 测试验证

```bash
# 运行历史记录检查测试
python3 test_history_check.py

# 运行完整告警组配置测试
python3 test_complete_groups.py
```

## 📊 完整的告警组监控配置

### 监控范围

系统现已扩展监控范围，从原来的2个告警组增加到**20个告警组**，全面覆盖商业化、影像、内容生态等核心业务：

**商业化相关 (8个)**:
- `commerce`, `commerce-tce`, `cc-commerce`
- `商业化`, `商业化|营销增长`, `商业化|商品支付`
- `商业化|权益供给`, `商业化|权益消费`

**影像相关 (3个)**:
- `影像|内容生态`, `影像|商业化`
- `影像|内容生态-sre-core`

**内容生态相关 (9个)**:
- `内容生态`, `内容生态|审核`, `内容生态客户端`
- `内容生态客户端测试`, `内容生态|模板工具`
- `内容生态|作者`, `内容生态|作者-sre-core`
- `内容生态|模板工具-sre-core`

### 监控条件

- **告警级别**: 仅监控 `critical` 级别告警
- **告警状态**: 仅监控 `firing` 状态告警
- **查询窗口**: 最近30分钟
- **超时设置**: 15秒，提高稳定性
- **数据过滤**: 排除空值和null值

## 📊 API 字段更新

### 新增响应字段

系统接口返回数据已更新，新增以下字段：

**任务列表接口 (`/task/list`) 和任务详情接口 (`/task/<create_time>/<fingerprint>`):**

- **`ai_status`**: AI盯盘状态
  - 来源: `ai_moniotr_history.status`
  - 可能值: `firing`, `suppressed`, `recover`
  - 说明: AI分析后的告警状态

- **`alarm_status`**: 告警状态
  - 来源: `alertman_history.status`
  - 可能值: `firing`, `resolved`, `suppressed`
  - 说明: 原始告警在告警系统中的状态

### 字段对比

| 字段名 | 数据来源 | 说明 |
|--------|----------|------|
| `ai_status` | `ai_moniotr_history.status` | AI分析后的状态 |
| `alarm_status` | `alertman_history.status` | 原始告警状态 |

### 使用场景

- **`ai_status`**: 查看AI盯盘的判断结果
- **`alarm_status`**: 查看告警在监控系统中的实际状态
- **状态对比**: 可以对比AI判断与实际告警状态的差异