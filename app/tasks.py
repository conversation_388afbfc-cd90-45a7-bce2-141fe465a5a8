#!/usr/bin/env python3
"""
后台任务模块 - 定期检索告警并创建盯盘任务
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple

from .utils import storage

# 设置日志等级为 INFO
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 设置特定模块的日志等级
logging.getLogger('app.handler.monitor.utils').setLevel(logging.INFO)
logging.getLogger('app.handler.monitor.handler').setLevel(logging.INFO)
logging.getLogger('app.utils').setLevel(logging.INFO)
logging.getLogger('app.tasks').setLevel(logging.INFO)

logger = logging.getLogger(__name__)

# 后台任务配置
BACKGROUND_TASK_CONFIG = {
    # 目标告警组 - 包含所有商业化、内容生态相关的组
    "target_groups": [
        # 商业化相关
        "commerce",
        "commerce-tce",
        "cc-commerce",
        "商业化",
        "商业化|营销增长",
        "商业化|商品支付",
        "商业化|权益供给",
        "商业化|权益消费",

        # 影像相关
        "影像|内容生态",
        "影像|商业化",
        "影像|内容生态-sre-core",

        # 内容生态相关
        "内容生态",
        "内容生态|审核",
        "内容生态客户端",
        "内容生态客户端测试",
        "内容生态|模板工具",
        "内容生态|作者",
        "内容生态|作者-sre-core",
        "内容生态|模板工具-sre-core",
    ],

    # 目标告警标题
    "target_titles": [
        "CC-SRE Server 下游可用率",
        "CC-SRE Server Method 可用率"
    ],

    # 执行间隔（秒）
    "check_interval": 45,

    # 查询时间窗口（分钟）
    "query_window_minutes": 30,

    # 并发处理数量
    "max_concurrent_tasks": 10
}

# 兼容性别名
TARGET_GROUPS = BACKGROUND_TASK_CONFIG["target_groups"]
TARGET_TITLES = BACKGROUND_TASK_CONFIG["target_titles"]

'''
SELECT
    create_time,
    fingerprint,
    status,
    alertname,
    alert_source,
    psm,
    level,
    title,
    `group`,
    content
FROM alertman_history
WHERE create_time >= NOW() - INTERVAL 30 MINUTE
    AND title IN ("CC-SRE Server 下游可用率", "CC-SRE Server Method 可用率")
    AND status = 'firing'
    AND level = 'critical'
    AND `group` not in ("sre_quota", "sre_debug", "sre")
ORDER BY create_time DESC
'''

@storage("monitor")
async def get_recent_alerts(db, cur, minutes: int = 30):
    """
    获取最近N分钟的告警记录

    Args:
        minutes: 查询最近多少分钟的数据

    Returns:
        List[Dict]: 告警记录列表
    """
    # 计算时间范围
    end_time = datetime.now()
    start_time = end_time - timedelta(minutes=minutes)

    # 查询需要AI分析的告警：最近的firing告警，只要告警还没恢复就持续分析
    sql = '''
    SELECT DISTINCT
        a.create_time,
        a.fingerprint,
        a.status,
        a.alertname,
        a.alert_source,
        a.psm,
        a.level,
        a.title,
        a.`group`,
        a.content
    FROM alertman_history a
    LEFT JOIN ai_moniotr_history h ON a.create_time = h.create_time AND a.fingerprint = h.fingerprint
    WHERE a.create_time >= %s
        AND a.create_time <= %s
        AND a.status = 'firing'
        AND (a.level = 'critical' or a.psm = 'sre.infra.polaris')
        AND not (a.psm = 'sre.infra.polaris' and a.`group` = 'p00-infra' and a.level = 'warning')
        AND a.`group` not in ("sre_quota", "sre_debug", "sre", "发布质检", "影像|服务架构")
        AND (h.status IS NULL OR h.status != 'recover')
    ORDER BY a.create_time DESC
    '''

    await cur.execute(sql, (
        start_time.strftime('%Y-%m-%d %H:%M:%S'),
        end_time.strftime('%Y-%m-%d %H:%M:%S')
    ))

    results = await cur.fetchall()

    # 转换为字典格式
    alerts = []
    for row in results:
        create_time, fingerprint, status, alertname, alert_source, psm, level, title, group, content = row
        alerts.append({
            'create_time': create_time.strftime('%Y-%m-%d %H:%M:%S') if create_time else None,
            'fingerprint': fingerprint,
            'status': status,
            'alertname': alertname,
            'alert_source': alert_source,
            'psm': psm,
            'level': level,
            'title': title,
            'group': group,
            'content': content
        })

    return alerts


async def process_alert_analysis(alert: Dict[str, Any], task_index: int) -> tuple[str, str, str]:
    """
    处理单个告警的AI分析

    Args:
        alert: 告警数据
        task_index: 任务索引

    Returns:
        tuple: (status, message, reason)
    """
    try:
        create_time = alert['create_time']
        fingerprint = alert['fingerprint']

        logger.info(f"[任务{task_index}] 开始分析告警: {fingerprint}")

        # 1. 检查告警是否已恢复
        from .handler.monitor.utils import check_alert_recover_status
        is_recovered = await check_alert_recover_status(create_time, fingerprint)
        if is_recovered:
            logger.info(f"[任务{task_index}] 告警已恢复: {fingerprint}")
            return "recover", "告警已恢复", "告警已恢复"

        # 2. 执行核心业务逻辑检查
        from .handler.monitor.handler import process_alert_core_logic
        alert_data = {
            'psm': alert['psm'],
            'title': alert['title'],
            'level': alert['level'],
            'content': alert['content'],
            'group': alert.get('group', 'unknown')
        }

        core_status, core_message, core_reason = await process_alert_core_logic(alert_data)

        if core_status == "firing":
            logger.info(f"[任务{task_index}] 核心逻辑返回firing: {fingerprint} - {core_reason}")
            return "firing", core_message, core_reason

        # 3. 检查AI阻断规则
        from .handler.monitor.utils import check_ai_block_rules
        alert_data_with_fingerprint = {**alert_data, 'fingerprint': fingerprint}
        is_blocked = await check_ai_block_rules(alert_data_with_fingerprint)

        if is_blocked:
            logger.info(f"[任务{task_index}] 命中AI阻断规则: {fingerprint}")
            return "suppressed", "命中AI阻断规则", "AI阻断规则"

        # 4. 执行AI分析
        logger.info(f"[任务{task_index}] 开始AI分析: {fingerprint}")
        ai_status, ai_message, ai_reason = await execute_ai_analysis(alert_data, create_time, fingerprint, task_index)

        logger.info(f"[任务{task_index}] AI分析完成: {fingerprint} -> {ai_status}")
        return ai_status, ai_message, ai_reason

    except Exception as e:
        logger.error(f"[任务{task_index}] 单个告警分析失败: {alert.get('fingerprint', 'unknown')} - {e}")
        return "firing", "AI分析异常", "AI分析异常"


async def execute_ai_analysis(alert_data: Dict[str, Any], create_time: str, fingerprint: str, task_index: int) -> tuple[str, str, str]:
    """
    执行AI分析

    Args:
        alert_data: 告警数据
        create_time: 创建时间
        fingerprint: 指纹
        task_index: 任务索引

    Returns:
        tuple: (status, message, reason)
    """
    try:
        # 调用AI分析逻辑 - 使用common处理器
        from .handler.monitor.handler import common

        logger.info(f"[任务{task_index}] 调用common处理器进行AI分析: {fingerprint}")

        status, message, reason = await common(
            alert_data=alert_data,
            create_time=create_time,
            fingerprint=fingerprint
        )

        logger.info(f"[任务{task_index}] AI分析完成: {fingerprint} -> {status}, 原因: {reason}")
        return status, message, reason

    except Exception as e:
        logger.error(f"[任务{task_index}] 执行AI分析失败: {fingerprint} - {e}")
        return "firing", "AI分析异常", "AI分析异常"


async def save_ai_analysis_result(create_time: str, fingerprint: str, status: str, message: str, alert: Dict[str, Any], reason: str = ""):
    """
    保存AI分析结果

    Args:
        create_time: 创建时间
        fingerprint: 指纹
        status: AI状态
        message: 消息
        alert: 告警数据
        reason: 原因
    """
    try:
        from .handler.monitor.utils import save_ai_monitor_history

        await save_ai_monitor_history(
            create_time=create_time,
            fingerprint=fingerprint,
            status=status,
            alertname=alert.get('title', ''),
            alert_source='background_analysis',
            psm=alert.get('psm', ''),
            level=alert.get('level', ''),
            title=alert.get('title', ''),
            group=alert.get('group', ''),
            content=alert.get('content', ''),
            reason=reason or message
        )

        logger.debug(f"保存AI分析结果成功: {fingerprint} -> {status}")

    except Exception as e:
        logger.error(f"保存AI分析结果失败: {fingerprint} - {e}")

async def create_monitoring_task(alert: Dict[str, Any], task_index: int = 0):
    """
    为告警创建AI分析任务 - 新架构版本

    Args:
        alert: 告警数据
        task_index: 任务索引，用于日志标识
    """
    try:
        create_time = alert['create_time']
        fingerprint = alert['fingerprint']

        logger.info(f"[任务{task_index}] 开始创建AI分析任务")
        logger.info(f"[任务{task_index}] 告警信息: fingerprint={fingerprint}, 告警组={alert['group']}, 标题={alert['title']}")
        logger.info(f"[任务{task_index}] 告警详情: psm={alert['psm']}, level={alert['level']}, create_time={create_time}")

        # 1. 检查是否已经有AI状态记录
        from .handler.monitor.utils import get_ai_analysis_status
        existing_status = await get_ai_analysis_status(create_time, fingerprint)

        if existing_status:
            status = existing_status['status']
            if status == 'recover':
                logger.info(f"[任务{task_index}] 告警已恢复: {status}，跳过处理")
                background_stats.update_stats(processed=1, created=0, failed=0)
                return
            else:
                logger.info(f"[任务{task_index}] 告警当前状态: {status}，继续AI分析更新状态")

        # 2. 执行AI分析流程
        start_time = datetime.now()
        data_status, message, reason = await process_alert_analysis(alert, task_index)
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        logger.info(f"[任务{task_index}] 分析完成，耗时: {duration:.2f}秒")
        logger.info(f"[任务{task_index}] 分析结果: status={data_status}")
        logger.info(f"[任务{task_index}] 分析原因: {reason}")

        # 3. 保存分析结果
        await save_ai_analysis_result(create_time, fingerprint, data_status, message, alert, reason)

        logger.info(f"[任务{task_index}] ✅ AI分析任务处理成功: {fingerprint} -> {data_status}")
        background_stats.update_stats(processed=1, created=1, failed=0)

    except Exception as e:
        logger.error(f"[任务{task_index}] 💥 创建AI分析任务异常: {alert.get('fingerprint', 'unknown')}, 错误: {e}")
        logger.error(f"[任务{task_index}] 异常详情:", exc_info=True)

        # 保存失败状态
        try:
            await save_ai_analysis_result(
                alert['create_time'],
                alert['fingerprint'],
                "firing",
                "AI分析异常",
                alert,
                "AI分析异常"
            )
        except:
            pass

        background_stats.update_stats(processed=1, created=0, failed=1)

async def process_alerts_batch(alerts: List[Dict[str, Any]]):
    """
    批量处理告警 - 异步创建任务，每个任务间隔0.05秒

    Args:
        alerts: 告警列表
    """
    if not alerts:
        logger.debug("📭 没有需要处理的告警")
        return

    logger.info(f"🚀 开始批量处理 {len(alerts)} 个告警")
    logger.info(f"⚙️  配置: 最大并发数={BACKGROUND_TASK_CONFIG['max_concurrent_tasks']}, 任务间隔=0.05秒")

    # 记录开始时间
    batch_start_time = datetime.now()

    # 并发处理告警，但限制并发数
    max_concurrent = BACKGROUND_TASK_CONFIG["max_concurrent_tasks"]
    semaphore = asyncio.Semaphore(max_concurrent)

    async def process_with_semaphore_and_delay(alert, index):
        """带信号量和延迟的处理函数"""
        try:
            # 添加延迟，每个任务间隔0.05秒
            if index > 0:
                delay = index * 0.05
                logger.debug(f"[任务{index}] 等待 {delay:.2f} 秒后开始处理...")
                await asyncio.sleep(delay)

            async with semaphore:
                logger.info(f"[任务{index}] 🎯 获取到处理槽位，开始处理告警: {alert.get('fingerprint', 'unknown')}")
                await create_monitoring_task(alert, task_index=index)
                logger.info(f"[任务{index}] ✅ 告警处理完成")

        except Exception as e:
            logger.error(f"[任务{index}] 💥 处理告警时发生异常: {e}")
            logger.error(f"[任务{index}] 异常详情:", exc_info=True)

    # 创建所有任务
    logger.info(f"📋 创建 {len(alerts)} 个异步任务...")
    tasks = []
    for i, alert in enumerate(alerts):
        task = process_with_semaphore_and_delay(alert, i + 1)
        tasks.append(task)
        logger.debug(f"[任务{i+1}] 任务已创建: {alert.get('fingerprint', 'unknown')}")

    # 等待所有任务完成
    logger.info(f"⏳ 等待所有任务完成...")
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 统计结果
    batch_end_time = datetime.now()
    batch_duration = (batch_end_time - batch_start_time).total_seconds()

    success_count = sum(1 for r in results if not isinstance(r, Exception))
    error_count = len(results) - success_count

    logger.info(f"🎉 批量处理完成!")
    logger.info(f"📊 处理统计: 总数={len(alerts)}, 成功={success_count}, 失败={error_count}")
    logger.info(f"⏱️  总耗时: {batch_duration:.2f} 秒")
    logger.info(f"📈 平均耗时: {batch_duration/len(alerts):.2f} 秒/任务")

    # 记录异常信息
    if error_count > 0:
        logger.warning(f"⚠️  有 {error_count} 个任务处理失败:")
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"   [任务{i+1}] 失败原因: {result}")

async def background_alert_monitor():
    """
    后台AI分析任务 - 每45秒执行一次

    执行流程:
    1. 扫描需要AI分析的告警（只要告警还没恢复就持续分析）
    2. 并发执行AI分析（时间阈值、AI阻断规则、AI分析）
    3. 更新AI状态到数据库
    """
    logger.info("🚀 启动后台AI分析任务")
    logger.info(f"⚙️  分析配置: 查询窗口={BACKGROUND_TASK_CONFIG['query_window_minutes']}分钟, 检查间隔={BACKGROUND_TASK_CONFIG['check_interval']}秒")
    logger.info(f"🔧 并发配置: 最大并发数={BACKGROUND_TASK_CONFIG['max_concurrent_tasks']}, 任务间隔=0.05秒")

    cycle_count = 0

    while True:
        cycle_count += 1
        cycle_start_time = datetime.now()

        try:
            logger.info(f"🔄 [周期{cycle_count}] 开始执行AI分析检查 - {cycle_start_time.strftime('%Y-%m-%d %H:%M:%S')}")

            # 获取需要AI分析的告警
            query_window = BACKGROUND_TASK_CONFIG["query_window_minutes"]
            logger.debug(f"🔍 [周期{cycle_count}] 查询最近 {query_window} 分钟需要AI分析的告警...")

            alerts = await get_recent_alerts(minutes=query_window)

            logger.info(f"📊 [周期{cycle_count}] 数据库查询结果: 找到 {len(alerts) if alerts else 0} 个待分析告警")

            if alerts:
                logger.info(f"📋 [周期{cycle_count}] 告警详情:")
                for i, alert in enumerate(alerts[:5]):  # 只显示前5个
                    logger.info(f"   {i+1}. {alert['fingerprint']} - {alert['group']} - {alert['title']} - {alert['status']}")
                if len(alerts) > 5:
                    logger.info(f"   ... 还有 {len(alerts) - 5} 个告警")

                # 暂时注释掉告警组过滤，直接处理所有查询到的告警
                logger.debug(f"🔍 [周期{cycle_count}] 暂时跳过告警组过滤，直接处理所有告警...")
                filtered_alerts = alerts  # 直接使用所有查询到的告警

                # # 过滤出符合条件的告警 (暂时注释)
                # filtered_alerts = []
                # for alert in alerts:
                #     group_match = alert['group'] in TARGET_GROUPS
                #     title_match = alert['title'] in TARGET_TITLES
                #     status_match = alert['status'] == 'firing'
                #
                #     logger.debug(f"   告警 {alert['fingerprint']}: 告警组匹配={group_match}, 标题匹配={title_match}, 状态匹配={status_match}")
                #
                #     if group_match and title_match and status_match:
                #         filtered_alerts.append(alert)
                #         logger.debug(f"   ✅ 告警 {alert['fingerprint']} 符合条件，加入处理队列")
                #     else:
                #         logger.debug(f"   ❌ 告警 {alert['fingerprint']} 不符合条件，跳过")

                logger.info(f"✅ [周期{cycle_count}] 处理告警: {len(alerts)} -> {len(filtered_alerts)} 个 (暂时未过滤)")

                if filtered_alerts:
                    logger.info(f"🎯 [周期{cycle_count}] 开始处理 {len(filtered_alerts)} 个告警:")
                    for i, alert in enumerate(filtered_alerts):
                        logger.info(f"   {i+1}. {alert['fingerprint']} - {alert['group']} - {alert['title']}")

                    await process_alerts_batch(filtered_alerts)
                    logger.info(f"✅ [周期{cycle_count}] 批量处理完成")
                else:
                    logger.debug(f"⏭️  [周期{cycle_count}] 没有告警需要处理")
            else:
                logger.debug(f"📭 [周期{cycle_count}] 没有发现新的告警")

        except Exception as e:
            logger.error(f"💥 [周期{cycle_count}] 后台告警监控任务执行失败: {e}")
            logger.error(f"💥 [周期{cycle_count}] 异常详情:", exc_info=True)

        # 计算本周期耗时
        cycle_end_time = datetime.now()
        cycle_duration = (cycle_end_time - cycle_start_time).total_seconds()
        logger.info(f"⏱️  [周期{cycle_count}] 本周期耗时: {cycle_duration:.2f} 秒")

        # 等待配置的间隔时间
        check_interval = BACKGROUND_TASK_CONFIG["check_interval"]
        logger.debug(f"😴 [周期{cycle_count}] 等待 {check_interval} 秒后开始下一周期...")
        await asyncio.sleep(check_interval)

# 统计信息
class BackgroundTaskStats:
    def __init__(self):
        self.start_time = datetime.now()
        self.processed_alerts = 0
        self.created_tasks = 0
        self.failed_tasks = 0
        self.last_run_time = None

    def update_stats(self, processed: int, created: int, failed: int):
        self.processed_alerts += processed
        self.created_tasks += created
        self.failed_tasks += failed
        self.last_run_time = datetime.now()

    def get_stats(self):
        uptime = datetime.now() - self.start_time
        return {
            "uptime_seconds": int(uptime.total_seconds()),
            "processed_alerts": self.processed_alerts,
            "created_tasks": self.created_tasks,
            "failed_tasks": self.failed_tasks,
            "last_run_time": self.last_run_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_run_time else None,
            "success_rate": f"{(self.created_tasks / max(self.processed_alerts, 1) * 100):.1f}%" if self.processed_alerts > 0 else "0%"
        }

# 全局统计实例
background_stats = BackgroundTaskStats()

async def background_recover_status_updater():
    """
    后台恢复状态更新任务 - 定期更新已恢复告警的状态

    执行流程:
    1. 每45秒执行一次
    2. 查找ai_moniotr_history中状态为suppressed的告警
    3. 检查对应的alertman_history状态是否为recover
    4. 如果是，则更新ai_moniotr_history状态为recover
    """
    logger.info("🔄 启动后台恢复状态更新任务")

    cycle_count = 0
    update_interval = 45  # 45秒执行一次

    while True:
        cycle_count += 1
        cycle_start_time = datetime.now()

        logger.info(f"🔄 [恢复更新{cycle_count}] 开始执行恢复状态更新 - {cycle_start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        try:
            from .handler.monitor.utils import update_recovered_alerts
            updated_count = await update_recovered_alerts()

            if updated_count > 0:
                logger.info(f"✅ [恢复更新{cycle_count}] 更新了 {updated_count} 个告警状态为recover")
            else:
                logger.debug(f"📭 [恢复更新{cycle_count}] 没有需要更新的告警")

        except Exception as e:
            logger.error(f"💥 [恢复更新{cycle_count}] 恢复状态更新任务执行失败: {e}")
            logger.error(f"💥 [恢复更新{cycle_count}] 异常详情:", exc_info=True)

        # 计算本周期耗时
        cycle_end_time = datetime.now()
        cycle_duration = (cycle_end_time - cycle_start_time).total_seconds()
        logger.info(f"⏱️  [恢复更新{cycle_count}] 本周期耗时: {cycle_duration:.2f} 秒")

        # 等待间隔时间
        logger.debug(f"😴 [恢复更新{cycle_count}] 等待 {update_interval} 秒后开始下一周期...")
        await asyncio.sleep(update_interval)


# 任务列表
task_list = [
    background_alert_monitor,
    background_recover_status_updater
]