
import base64
import json
import httpx
import logging
import os
import asyncio
import re
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
from ...utils import storage
from ...utils import get_region

logger = logging.getLogger(__name__)


def process_url_time_params(url: str) -> str:
    """
    处理Grafana URL中的时间参数
    将时间参数替换为最近33分钟的数据，endtime往前推60秒

    Args:
        url: 原始 URL

    Returns:
        str: 处理后的URL
    """
    try:
        # 计算时间戳（毫秒）
        now = datetime.now()
        # endtime往前推60秒
        end_time = now - timedelta(seconds=60)
        # 开始时间为endtime往前推33分钟
        start_time = end_time - timedelta(minutes=33)

        # 转换为毫秒时间戳
        from_timestamp = int(start_time.timestamp() * 1000)
        to_timestamp = int(end_time.timestamp() * 1000)

        logger.info(f"[URL] 计算时间参数:")
        logger.info(f"   开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')} ({from_timestamp})")
        logger.info(f"   结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')} ({to_timestamp})")

        # 解析URL
        parsed = urlparse(url)
        query_params = parse_qs(parsed.query)

        # 更新时间参数
        query_params['from'] = [str(from_timestamp)]
        query_params['to'] = [str(to_timestamp)]

        # 重新构建URL
        new_query = urlencode(query_params, doseq=True)
        new_parsed = parsed._replace(query=new_query)
        new_url = urlunparse(new_parsed)

        logger.info(f"[URL] 时间参数更新完成")
        return new_url

    except Exception as e:
        logger.error(f"[URL] 处理时间参数失败: {e}")
        logger.error(f"[URL] 返回原始URL: {url}")
        return url


async def format_graph_base64(image_data: bytes) -> str:
    """将图片数据转换为base64格式"""
    try:
        base64_data = base64.b64encode(image_data).decode('utf-8')
        return f"data:image/png;base64,{base64_data}"
    except Exception as e:
        logger.error(f"图片转base64失败: {e}")
        raise


async def get_ai_response(base64_image: str, create_time: str = "", dependency_context: str = "", alert_source: str = "", max_retries: int = 3) -> Dict[str, Any]:
    """调用AI模型进行多模态分析"""

    # 读取提示词
    try:
        # 根据图表类型选择不同的提示词文件
        if alert_source in {'kepler', 'polaris'}:
            prompt_filename = 'kepler_polaris.md'
        else:  # argos, grafana 或其他类型
            prompt_filename = 'argos_grafana.md'

        prompt_path = os.path.join(os.path.dirname(__file__), f'../../prompt/monitor/{prompt_filename}')
        logger.info(f"使用prompt文件: {prompt_filename} (图表类型: {alert_source})")

        with open(prompt_path, 'r', encoding='utf-8') as f:
            system_prompt = f.read()

        try:
            from datetime import datetime, timedelta

            # 解析create_time并计算时间增量
            dt = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')
            create_time_del_10min = (dt - timedelta(minutes=10)).strftime('%Y-%m-%d %H:%M:%S')
            create_time_add_3min = (dt + timedelta(minutes=3)).strftime('%Y-%m-%d %H:%M:%S')
            create_time_add_5min = (dt + timedelta(minutes=5)).strftime('%Y-%m-%d %H:%M:%S')
            create_time_add_10min = (dt + timedelta(minutes=10)).strftime('%Y-%m-%d %H:%M:%S')

            # 使用format方法一次性替换所有占位符
            system_prompt = system_prompt.format(
                create_time=create_time,
                create_time_del_10min=create_time_del_10min,
                create_time_add_3min=create_time_add_3min,
                create_time_add_5min=create_time_add_5min,
                create_time_add_10min=create_time_add_10min,
                alert_source=alert_source
            )

            logger.debug(f"时间字段渲染完成: {create_time} -> -10min: {create_time_del_10min}, +3min: {create_time_add_3min}, +5min: {create_time_add_5min}, +10min: {create_time_add_10min}")

        except Exception as e:
            logger.error(f"计算时间增量失败: {e}")
            raise

    except Exception as e:
        logger.error(f"读取提示词文件失败: {e}")
        raise

    # 构建用户消息，包含业务依赖上下文
    user_text = "请分析这张监控图片，判断告警状态。"
    if dependency_context:
        user_text += f"\n\n业务上下文：{dependency_context}"

    logger.info(f"[AI_Prompt] prompt总长度: {len(system_prompt)} 字符")
    logger.info(f"[AI_Prompt] prompt: {system_prompt}")

    # 构建请求消息
    messages = [
        {
            "role": "system",
            "content": system_prompt
        },
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": user_text
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": base64_image
                    }
                }
            ]
        }
    ]

    # 获取配置
    api_key = os.getenv('MODEL_API_KEY')
    if not api_key:
        raise ValueError("MODEL_API_KEY 环境变量未设置")

    headers = {
        "Content-Type": "application/json",
        'Authorization': f'Bearer {api_key}',
    }

    # 重试机制 - 最多重试3次
    last_error = None
    for attempt in range(max_retries + 1):
        try:
            logger.info(f"AI分析请求 - 第 {attempt + 1} 次尝试 (最多 {max_retries + 1} 次)")

            async with httpx.AsyncClient() as client:
                request_data = {
                    "model": "ep-20250715121128-kpg25",
                    "messages": messages,
                    "thinking":{
                        "type": "enabled"
                    }
                }
                url = "https://ark-cn-beijing.bytedance.net/api/v3/chat/completions"

                logger.info(f"[AI_Request] 第 {attempt + 1} 次请求 - URL: {url}")
                logger.info(f"[AI_Request] 第 {attempt + 1} 次请求 - Headers: {headers}")
                logger.info(f"[AI_Request] 第 {attempt + 1} 次请求 - Model: {request_data['model']}")
                logger.info(f"[AI_Request] 第 {attempt + 1} 次请求 - Messages count: {len(request_data['messages'])}")

                response = await client.post(
                    url=url,
                    headers=headers,
                    json=request_data,
                    timeout=120
                )

                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"[AI_Request] 第 {attempt + 1} 次请求成功 - Status: {response.status_code}")
                    logger.info(f"[AI_Request] 第 {attempt + 1} 次请求成功 - Response keys: {list(result.keys())}")

                    # 提取AI响应内容
                    ai_content = ""
                    choices = result.get("choices", [])
                    logger.info(f"[AI_Request] 第 {attempt + 1} 次请求 - Choices count: {len(choices)}")

                    for item in choices:
                        if item.get("message", {}).get("role") == "assistant":
                            ai_content = item["message"]["content"]
                            logger.info(f"[AI_Request] 第 {attempt + 1} 次请求 - AI content length: {len(ai_content)}")
                            break

                    if not ai_content:
                        logger.error(f"[AI_Request] 第 {attempt + 1} 次请求 - AI响应为空")
                        logger.error(f"[AI_Request] 第 {attempt + 1} 次请求 - Full response: {result}")
                        raise ValueError("AI响应为空")

                    # 解析JSON响应
                    try:
                        # 尝试直接解析JSON
                        ai_result = json.loads(ai_content)

                        # 验证响应格式 - 检查必需字段
                        validation_errors = []

                        if "status" not in ai_result:
                            validation_errors.append("缺少status字段")
                        elif ai_result["status"] not in ["firing", "suppressed"]:
                            validation_errors.append(f"status值无效: {ai_result['status']}")

                        if "response" not in ai_result:
                            validation_errors.append("缺少response字段")
                        elif not isinstance(ai_result["response"], str) or not ai_result["response"].strip():
                            validation_errors.append("response字段为空或格式无效")

                        if validation_errors:
                            logger.error(f"[AI_Request] 第 {attempt + 1} 次请求 - 验证失败: {', '.join(validation_errors)}")
                            logger.error(f"[AI_Request] 第 {attempt + 1} 次请求 - AI content: {ai_content[:500]}...")
                            raise ValueError(f"AI响应验证失败: {', '.join(validation_errors)}")

                        logger.info(f"[AI_Request] 第 {attempt + 1} 次请求 - 验证成功: status={ai_result['status']}, response长度={len(ai_result['response'])}")
                        return ai_result

                    except json.JSONDecodeError as json_error:
                        logger.error(f"[AI_Request] 第 {attempt + 1} 次请求 - JSON解析失败: {json_error}")
                        logger.error(f"[AI_Request] 第 {attempt + 1} 次请求 - AI content: {ai_content[:500]}...")

                        # 如果直接解析失败，尝试提取JSON部分
                        import re
                        json_match = re.search(r'\{[^}]*"status"[^}]*"response"[^}]*\}', ai_content)
                        if json_match:
                            try:
                                extracted_json = json_match.group()
                                logger.info(f"[AI_Request] 第 {attempt + 1} 次请求 - 尝试提取JSON: {extracted_json}")
                                ai_result = json.loads(extracted_json)
                                # 再次验证提取的JSON
                                if (ai_result.get("status") in ["firing", "suppressed"] and
                                    ai_result.get("response") and
                                    isinstance(ai_result.get("response"), str)):
                                    logger.info(f"[AI_Request] 第 {attempt + 1} 次请求 - 从文本中提取JSON成功: {ai_result}")
                                    return ai_result
                            except json.JSONDecodeError as extract_error:
                                logger.error(f"[AI_Request] 第 {attempt + 1} 次请求 - 提取的JSON也无法解析: {extract_error}")

                        raise ValueError(f"AI响应格式不正确，无法解析JSON: {ai_content[:200]}...")

                else:
                    logger.error(f"[AI_Request] 第 {attempt + 1} 次请求 - HTTP错误")
                    logger.error(f"[AI_Request] 第 {attempt + 1} 次请求 - Status: {response.status_code}")
                    logger.error(f"[AI_Request] 第 {attempt + 1} 次请求 - Response: {response.text}")
                    logger.error(f"[AI_Request] 第 {attempt + 1} 次请求 - Headers: {response.headers}")
                    raise httpx.HTTPStatusError(f"HTTP {response.status_code}: {response.text}",
                                              request=response.request, response=response)

        except Exception as e:
            last_error = e
            logger.error(f"[AI_Request] 第 {attempt + 1} 次请求失败: {type(e).__name__}: {e}")

            # 记录详细的错误信息
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"[AI_Request] 第 {attempt + 1} 次请求 - Response status: {e.response.status_code}")
                logger.error(f"[AI_Request] 第 {attempt + 1} 次请求 - Response text: {e.response.text}")

            if attempt == max_retries:
                logger.error(f"[AI_Request] 重试已达上限 ({max_retries + 1} 次)，最终失败")
                logger.error(f"[AI_Request] 最终错误: {type(e).__name__}: {e}")
                raise
            else:
                logger.info(f"[AI_Request] 准备进行第 {attempt + 2} 次重试...")
                # 短暂延迟后重试
                import asyncio
                await asyncio.sleep(1)

    # 如果所有重试都失败了
    raise Exception(f"AI请求失败，已达最大重试次数 ({max_retries + 1} 次)，最后错误: {last_error}")


async def get_token_jwt():
    """获取JWT Token"""
    try:
        # 这里需要根据实际配置获取base配置
        from ...config import base  # 假设配置在这里

        async with httpx.AsyncClient(verify=False) as client:
            response = await client.get(
                url=base["AUTH_URL"],
                headers={"Authorization": f"Bearer {base['JWT_TOKEN']}"}
            )
            x_token_jwt = response.headers.get("X-Jwt-Token")
            return x_token_jwt
    except Exception as e:
        logger.error(f"获取JWT Token失败: {e}")
        raise


@storage("monitor")
async def get_graph_url_body_with_url(db, cur, fingerprint):
    """通过fingerprint获取graph_key并生成图表，同时返回原始URL和source

    Args:
        fingerprint: 告警指纹

    Returns:
        tuple: (image_data, chart_url, source)
    """
    chart_url = ""  # 保存原始URL

    try:
        # 优先查询 grafana 类型的图表
        grafana_sql = f'''SELECT source, value, vregion
FROM feishu_card_graph_info
WHERE fingerprint = "{fingerprint}" AND source = "grafana"
AND create_time = (
    SELECT create_time
    FROM feishu_card_graph_info
    WHERE fingerprint = "{fingerprint}" AND source = "grafana" and value != "" and value is not null
    ORDER BY create_time DESC
    LIMIT 1
) and value != "" and value is not null
LIMIT 1'''

        logger.info(f'[Card_Graph] 优先查询grafana图表 - fingerprint: {fingerprint}')
        logger.info(f'[Card_Graph] Grafana SQL: {grafana_sql}')

        await cur.execute(grafana_sql)
        grafana_result = await cur.fetchall()

        if len(grafana_result) > 0:
            source, value, vregion = grafana_result[0][0], grafana_result[0][1], grafana_result[0][2]
            # 检查grafana的value是否以http开头
            if value and value.startswith('http'):
                chart_url = value  # 保存原始URL
                logger.info(f"[Card_Graph] Found valid grafana chart: source={source}, value={value}")
            else:
                logger.info(f"[Card_Graph] Grafana value不是http开头，认为无效: {value}")
                grafana_result = []  # 重置为空，继续查询其他类型

        # 如果grafana无效或没有找到，查询其他类型
        if len(grafana_result) == 0:
            logger.info(f'[Card_Graph] 未找到有效的grafana图表，查询其他类型')

            other_sql = f'''SELECT source, value, vregion
FROM feishu_card_graph_info
WHERE fingerprint = "{fingerprint}" AND source != "grafana"
AND create_time = (
    SELECT create_time
    FROM feishu_card_graph_info
    WHERE fingerprint = "{fingerprint}" AND source != "grafana" and value != "" and value is not null
    ORDER BY create_time DESC
    LIMIT 1
) and value != "" and value is not null
LIMIT 1'''

            logger.info(f'[Card_Graph] Other types SQL: {other_sql}')
            await cur.execute(other_sql)
            other_result = await cur.fetchall()

            if len(other_result) == 0:
                logger.warning(f"[Card_Graph] No value found for fingerprint: {fingerprint}")
                return b"", "", ""

            source, value, vregion = other_result[0][0], other_result[0][1], other_result[0][2]
            chart_url = value  # 保存原始URL
            logger.info(f"[Card_Graph] Found other type chart: source={source}, value={value}")

    except Exception as err:
        logger.error(f'[Card_Graph] Database query failed for {fingerprint}: {err}')
        return b"", "", ""

    # 获取配置
    try:
        from ...config import base
    except ImportError:
        logger.error("无法导入配置，请确保config模块存在")
        return b"", chart_url, source

    # 获取JWT Token（如果需要）
    try:
        x_token_jwt = await get_token_jwt()
    except Exception as e:
        logger.warning(f"获取JWT Token失败: {e}")
        x_token_jwt = None

    # 重试机制
    for retry_num in range(1, 4):
        try:
            async with httpx.AsyncClient(verify=False) as client:
                if source == 'argos':
                    logger.info(f'[Card_Graph] argos graph')
                    data = {
                        'bosun': value,
                        'vregion': vregion
                    }
                    headers = {
                        'Content-Type': 'application/json'
                    }
                    url = f'{base["BASE_URL"]}/api/v1/kepler/generate_graph/{get_region()}/0'
                    url = process_url_time_params(url)
                    logger.info(f"[Card_Graph] Argos request - URL: {url}")
                    logger.info(f"[Card_Graph] Argos request - Headers: {headers}")
                    logger.info(f"[Card_Graph] Argos request - Data: {data}")

                    try:
                        response = await client.post(
                            url=url,
                            timeout=15,
                            headers=headers,
                            json=data
                        )
                        response.raise_for_status()
                        body = response.content
                        logger.info(f"[Card_Graph] Argos graph generation success - response length: {len(body)} bytes")
                        return body, chart_url, source
                    except Exception as e:
                        logger.error(f"[Card_Graph] Argos request failed")
                        logger.error(f"[Card_Graph] Argos error - URL: {url}")
                        logger.error(f"[Card_Graph] Argos error - Headers: {headers}")
                        logger.error(f"[Card_Graph] Argos error - Data: {data}")
                        logger.error(f"[Card_Graph] Argos error - Exception: {e}")
                        if hasattr(e, 'response') and e.response is not None:
                            logger.error(f"[Card_Graph] Argos error - Status: {e.response.status_code}")
                            logger.error(f"[Card_Graph] Argos error - Response: {e.response.text}")
                        raise

                elif source == 'kepler':
                    logger.info(f'[Card_Graph] kepler graph')
                    headers = {
                        'Content-Type': 'application/json'
                    }
                    url = f'{base["BASE_URL"]}/api/v1/kepler/generate_graph/{get_region()}/{value}'
                    url = process_url_time_params(url)
                    logger.info(f"[Card_Graph] Kepler request - URL: {url}")
                    logger.info(f"[Card_Graph] Kepler request - Headers: {headers}")
                    logger.info(f"[Card_Graph] Kepler request - Value: {value}")

                    try:
                        response = await client.post(
                            url=url,
                            timeout=15,
                            headers=headers
                        )
                        response.raise_for_status()
                        body = response.content
                        logger.info(f"[Card_Graph] Kepler graph generation success - response length: {len(body)} bytes")
                        return body, chart_url, source
                    except Exception as e:
                        logger.error(f"[Card_Graph] Kepler request failed")
                        logger.error(f"[Card_Graph] Kepler error - URL: {url}")
                        logger.error(f"[Card_Graph] Kepler error - Headers: {headers}")
                        logger.error(f"[Card_Graph] Kepler error - Value: {value}")
                        logger.error(f"[Card_Graph] Kepler error - Exception: {e}")
                        if hasattr(e, 'response') and e.response is not None:
                            logger.error(f"[Card_Graph] Kepler error - Status: {e.response.status_code}")
                            logger.error(f"[Card_Graph] Kepler error - Response: {e.response.text}")
                        raise

                elif source == 'polaris':
                    logger.info(f'[Card_Graph] polaris graph')
                    headers = {
                        'Content-Type': 'application/json'
                    }
                    data = {
                        "fingerprint": fingerprint,
                        'sql': value
                    }
                    url = f'{base["BASE_URL"]}/api/v1/kepler/generate_graph/{get_region()}/1'
                    url = process_url_time_params(url)
                    logger.info(f"[Card_Graph] Polaris request - URL: {url}")
                    logger.info(f"[Card_Graph] Polaris request - Headers: {headers}")
                    logger.info(f"[Card_Graph] Polaris request - Data: {data}")

                    try:
                        response = await client.post(
                            url=url,
                            timeout=15,
                            headers=headers,
                            json=data
                        )
                        response.raise_for_status()
                        body = response.content
                        logger.info(f"[Card_Graph] Polaris graph generation success - response length: {len(body)} bytes")
                        return body, chart_url, source
                    except Exception as e:
                        logger.error(f"[Card_Graph] Polaris request failed")
                        logger.error(f"[Card_Graph] Polaris error - URL: {url}")
                        logger.error(f"[Card_Graph] Polaris error - Headers: {headers}")
                        logger.error(f"[Card_Graph] Polaris error - Data: {data}")
                        logger.error(f"[Card_Graph] Polaris error - Exception: {e}")
                        if hasattr(e, 'response') and e.response is not None:
                            logger.error(f"[Card_Graph] Polaris error - Status: {e.response.status_code}")
                            logger.error(f"[Card_Graph] Polaris error - Response: {e.response.text}")
                        raise

                elif source == 'grafana':
                    logger.info(f'[Card_Graph] grafana graph')
                    processed_url = process_url_time_params(value)
                    # 处理Grafana URL时间参数
                    logger.info(f"[Card_Graph] 原始URL: {value}")
                    logger.info(f"[Card_Graph] 处理后URL: {processed_url}")

                    _token = await get_token_jwt()
                    data = {
                        "url": processed_url,
                        "width": 500,
                        "height": 250,
                        "device_scale_factor": 0,
                        "diagnosis": True,
                    }
                    headers = {
                        'X-JWT-Token': _token,
                        'Content-Type': 'application/json'
                    }
                    url = f'{base["GRAFANA_OPEN_API"]}/api/v1/grafana_open_api/screenshot'

                    logger.info(f"[Card_Graph] Grafana request - URL: {url}")
                    logger.info(f"[Card_Graph] Grafana request - Headers: {headers}")
                    logger.info(f"[Card_Graph] Grafana request - Data: {data}")

                    try:
                        response = await client.post(
                            url=url,
                            timeout=15,
                            headers=headers,
                            json=data
                        )
                        response.raise_for_status()
                        body = response.content
                        logger.info(f"[Card_Graph] Grafana graph generation success - response length: {len(body)} bytes")
                        return body, chart_url, source
                    except Exception as e:
                        logger.error(f"[Card_Graph] Grafana request failed")
                        logger.error(f"[Card_Graph] Grafana error - URL: {url}")
                        logger.error(f"[Card_Graph] Grafana error - Headers: {headers}")
                        logger.error(f"[Card_Graph] Grafana error - Data: {data}")
                        logger.error(f"[Card_Graph] Grafana error - Exception: {e}")
                        if hasattr(e, 'response') and e.response is not None:
                            logger.error(f"[Card_Graph] Grafana error - Status: {e.response.status_code}")
                            logger.error(f"[Card_Graph] Grafana error - Response: {e.response.text}")
                        raise

                else:
                    logger.error(f"[Card_Graph] Unsupported source: {source}")
                    return b"", chart_url, source

        except Exception as err:
            logger.warning(f'[Card_Graph] Attempt {retry_num} failed: {err}')
            if retry_num <= 2:
                await asyncio.sleep(2 ** retry_num * 0.4)

    logger.error(f"[Card_Graph] All retry attempts failed for fingerprint: {fingerprint}")
    return b"", chart_url, source

async def get_graph_image_data_with_url(fingerprint: str) -> tuple[bytes, str, str]:
    """
    根据fingerprint获取图表图片数据、URL和source

    Args:
        fingerprint: 告警指纹

    Returns:
        tuple: (图片的二进制数据, 原始URL, source)
    """
    try:
        if not fingerprint:
            raise ValueError("fingerprint 参数不能为空")

        logger.info(f"获取图表数据、URL和source，fingerprint: {fingerprint}")

        # 调用图表生成函数，同时获取URL和source
        image_data, chart_url, source = await get_graph_url_body_with_url(fingerprint)

        if not image_data:
            raise ValueError("图表生成失败，返回空数据")

        return image_data, chart_url, source

    except Exception as e:
        logger.error(f"获取图表图片数据、URL和source失败: {e}")
        raise


@storage("monitor")
async def get_alert_item(db, cur, create_time, fingerprint):
    """
    根据时间和fingerprint查询告警信息
    注意：数据库中的fingerprint已经是labels._fingerprint的值
    """
    sql = '''
    select
        psm, title, level, content, `group`
    from
        alertman_history
    where
        create_time = %s
        and fingerprint = %s
    group by 1, 2, 3, 4, 5
    '''
    await cur.execute(sql, (create_time, fingerprint))
    return await cur.fetchall()

# 暂时只检查第一层
@storage("monitor")
async def check_business_strong_dependency(db, cur, psm):
    """检查PSM是否为业务场景的强依赖"""
    sql = '''
    select distinct f_fingerprint
    from cmdb.server_core_pathway_backend_link_v2
    where swd_mark = 'strong'
    and service = %s
    and dep_level = 1
'''
    await cur.execute(sql, (psm,))
    result = await cur.fetchall()
    return len(result) > 0, result

@storage("monitor")
async def get_ai_monitor_history_status(db, cur, create_time, fingerprint):
    """查询AI监控历史记录的状态"""
    sql = '''
    select status from ai_moniotr_history
    where create_time = %s and fingerprint = %s
    '''
    await cur.execute(sql, (create_time, fingerprint))
    result = await cur.fetchone()
    return result[0] if result else None

@storage("monitor")
async def get_active_monitoring_tasks_count(db, cur):
    """获取当前正在执行的盯盘任务数量"""
    sql = '''
    select count(*) as count
    from ai_moniotr_history
    where create_time >= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
    '''
    await cur.execute(sql)
    result = await cur.fetchone()
    return result[0] if result else 0

@storage("monitor")
async def get_alarm_status_batch_by_keys(db, cur, task_keys):
    """
    根据 (create_time, fingerprint) 批量查询告警状态

    Args:
        task_keys: List of (create_time, fingerprint) tuples

    Returns:
        Dict: {(create_time, fingerprint): status}
    """
    if not task_keys:
        return {}

    # 构建批量查询条件
    conditions = []
    params = []

    for create_time, fingerprint in task_keys:
        conditions.append("(create_time = %s AND fingerprint = %s)")
        params.extend([create_time, fingerprint])

    sql = f'''
    select create_time, fingerprint, status
    from alertman_history
    where {' OR '.join(conditions)}
    order by create_time desc
    '''

    await cur.execute(sql, params)
    results = await cur.fetchall()

    # 转换为字典格式
    status_map = {}
    for row in results:
        create_time, fingerprint, status = row
        key = (create_time, fingerprint)
        if key not in status_map:  # 只取第一个结果（最新的）
            status_map[key] = status

    logger.debug(f"批量查询告警状态: 查询 {len(task_keys)} 个，找到 {len(status_map)} 个")
    return status_map



@storage("monitor")
async def save_ai_monitor_history(db, cur, create_time, fingerprint, status, alertname, alert_source, psm, level, title, group, content, reason=""):
    """
    保存AI监控历史记录
    如果AI认为是firing了，则不再进入suppressed状态以及recover
    """
    try:
        # 先尝试查询记录是否存在以及当前状态
        check_sql = '''
        select count(1) as count, status from ai_moniotr_history
        where create_time = %s and fingerprint = %s
        group by status
        '''
        await cur.execute(check_sql, (create_time, fingerprint))
        result = await cur.fetchone()
        exists = result[0] > 0 if result else False
        current_status = result[1] if result else None

        if exists:
            # 记录存在，检查状态转换规则
            # 如果当前状态是firing，则不允许更新
            if current_status == 'firing' :
                logger.info(f"AI状态为firing，不允许转换为{status}: {fingerprint}, 当前状态: {current_status}, 尝试更新为: {status}")
                return  # 直接返回，不执行更新

            # 记录存在，执行更新
            update_sql = '''
            update ai_moniotr_history set
            status = %s,
            alertname = %s,
            alert_source = %s,
            psm = %s,
            level = %s,
            title = %s,
            `group` = %s,
            content = %s,
            reason = %s
            where create_time = %s and fingerprint = %s
            '''
            await cur.execute(update_sql, (status, alertname, alert_source, psm, level, title, group, content, reason, create_time, fingerprint))
            logger.debug(f"更新AI监控历史记录成功: {fingerprint}, status={status}, reason={reason}")
        else:
            # 记录不存在，执行插入
            insert_sql = '''
            insert into ai_moniotr_history
            (create_time, fingerprint, status, alertname, alert_source, psm, level, title, `group`, content, reason)
            values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            '''
            await cur.execute(insert_sql, (create_time, fingerprint, status, alertname, alert_source, psm, level, title, group, content, reason))
            logger.debug(f"插入AI监控历史记录成功: {fingerprint}, status={status}, reason={reason}")

    except Exception as e:
        logger.error(f"保存AI监控历史记录失败: create_time={create_time}, fingerprint={fingerprint}, status={status}, error={e}")
        logger.error(f"详细参数: alertname={alertname}, psm={psm}, level={level}, title={title}, group={group}, reason={reason}")
        raise


@storage("monitor")
async def save_chart_record(db, cur, create_time, fingerprint, url, base64_content, ai_response=None):
    """保存图表base64记录到ai_moniotr_recored表"""
    sql = '''
    insert into ai_moniotr_recored
    (create_time, fingerprint, create_at, url, content, response)
    values (%s, %s, NOW(), %s, %s, %s)
    '''
    # 提取AI响应中的response字段
    response_str = ""
    if ai_response:
        try:
            # 如果ai_response是字典，提取response字段
            if isinstance(ai_response, dict):
                response_str = ai_response.get('response', '')
            else:
                # 如果是字符串，尝试解析JSON后提取response字段
                import json
                parsed_response = json.loads(str(ai_response))
                response_str = parsed_response.get('response', '')
        except Exception as e:
            logger.error(f"提取AI响应response字段失败: {e}")
            # 如果提取失败，保存整个响应
            response_str = str(ai_response)

    await cur.execute(sql, (create_time, fingerprint, url, base64_content, response_str))
    logger.info(f"保存图表记录成功: {fingerprint}, URL: {url[:100]}...")


@storage("monitor")
async def get_chart_record(db, cur, create_time, fingerprint):
    """获取图表base64记录"""
    sql = '''
    select url, content, response, create_at
    from ai_moniotr_recored
    where create_time = %s and fingerprint = %s
    order by create_at desc
    limit 1
    '''
    await cur.execute(sql, (create_time, fingerprint))
    result = await cur.fetchone()
    return result


@storage("monitor")
async def get_task_list(db, cur):
    """获取最近100个分析任务列表"""
    sql = '''
    select create_time, fingerprint, status, alertname, alert_source, psm, level, title, `group`, content, reason
    from ai_moniotr_history
    order by create_time desc
    limit 100
    '''
    await cur.execute(sql)
    result = await cur.fetchall()
    return result


@storage("monitor")
async def get_task_detail(db, cur, create_time, fingerprint):
    """
    获取某个任务的详细信息
    仅支持通过 create_time 和 fingerprint 查询，如果参数为空则返回 null

    Args:
        create_time: 告警创建时间，必填
        fingerprint: 告警指纹，必填

    Returns:
        dict: 任务详细信息，如果参数为空或未找到则返回 None
    """
    # 参数验证：如果 create_time 或 fingerprint 为空，直接返回 None
    if not create_time or not fingerprint:
        logger.warning(f"get_task_detail 参数不完整: create_time={create_time}, fingerprint={fingerprint}")
        return None

    # 获取任务基本信息
    task_sql = '''
    select create_time, fingerprint, status, alertname, alert_source, psm, level, title, `group`, content, reason
    from ai_moniotr_history
    where create_time = %s and fingerprint = %s
    '''
    await cur.execute(task_sql, (create_time, fingerprint))
    task_info = await cur.fetchall()

    if not task_info:
        logger.info(f"未找到任务信息: create_time={create_time}, fingerprint={fingerprint}")
        return None

    # 获取步骤信息（从图表记录表）
    steps_sql = '''
    select
        create_at,
        response, content
    from ai_moniotr_recored
    where create_time = %s and fingerprint = %s
    group by 1, 2, 3
    order by create_at asc
    '''
    await cur.execute(steps_sql, (create_time, fingerprint))
    steps_info = await cur.fetchall()

    # 格式化返回数据
    if task_info:
        main_task = task_info[0]  # 取第一条作为主要任务信息
        create_time_val, fingerprint_val, ai_status, alertname, alert_source, psm, level, title, group, content, reason = main_task

        # 查询 alertman_history 获取真实的告警状态
        alarm_status_sql = '''
        select status from alertman_history
        where create_time = %s and fingerprint = %s
        order by create_time desc
        limit 1
        '''
        await cur.execute(alarm_status_sql, (create_time_val, fingerprint_val))
        alarm_status_result = await cur.fetchone()
        alarm_status = alarm_status_result[0] if alarm_status_result else "unknown"

        task_data = {
            "task_info": {
                "create_time": create_time_val.strftime('%Y-%m-%d %H:%M:%S') if create_time_val else None,
                "fingerprint": fingerprint_val,
                "ai_status": ai_status,  # AI盯盘状态
                "alarm_status": alarm_status,  # 告警状态
                "alertname": alertname,
                "alert_source": alert_source,
                "psm": psm,
                "level": level,
                "title": title,
                "group": group,
                "content": content,
                "reason": reason
            },
            "steps": []
        }

        # 添加步骤信息
        for i, step in enumerate(steps_info):
            create_at, response, content  = step
            task_data["steps"].append({
                "step_id": i + 1,
                "create_at": create_at.strftime('%Y-%m-%d %H:%M:%S') if create_at else None,
                "content": content,
                "response": response
            })

        return task_data

    return None


@storage("monitor")
async def get_task_steps(db, cur, create_time, fingerprint):
    """获取任务的每个步骤详情"""
    sql = '''
    select create_time, fingerprint, create_at, url, content, response
    from ai_moniotr_recored
    where create_time = %s and fingerprint = %s
    order by create_at asc
    '''
    await cur.execute(sql, (create_time, fingerprint))
    result = await cur.fetchall()
    return result


@storage("monitor")
async def check_recent_business_alerts(db, cur, f_fingerprints):
    """检查最近1小时的业务告警"""
    if not f_fingerprints:
        return []

    placeholders = ','.join(['%s'] * len(f_fingerprints))
    sql = f'''
    select title
    from server_core_pathway_bs_v2
    where f_fingerprint in ({placeholders})
    and create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    and status != 'recover'
'''
    await cur.execute(sql, f_fingerprints)
    return await cur.fetchall()


@storage("monitor")
async def check_alert_recover_status(db, cur, create_time, fingerprint):
    """
    检查告警是否已经恢复

    Args:
        create_time: 告警创建时间
        fingerprint: 告警指纹

    Returns:
        bool: True表示已恢复，False表示未恢复
    """
    try:
        sql = '''
        select status
        from alertman_history
        where create_time = %s and fingerprint = %s
        order by create_time desc
        limit 1
        '''
        await cur.execute(sql, (create_time, fingerprint))
        result = await cur.fetchone()

        if result and result[0] == 'recover':
            logger.info(f"告警已恢复: {fingerprint}")
            return True

        return False

    except Exception as e:
        logger.error(f"检查告警恢复状态失败: {e}")
        return False


@storage("monitor")
async def update_recovered_alerts(db, cur):
    """
    更新已恢复告警的状态

    查找ai_moniotr_history中状态为suppressed的告警，
    如果对应的alertman_history状态为recover，则更新ai_moniotr_history状态为recover

    注意：如果AI认为是firing了，则不再进入suppressed状态以及recover

    Returns:
        int: 更新的记录数
    """
    try:
        # 查找需要更新的记录，排除AI状态曾经为firing的记录
        query_sql = '''
        SELECT a.create_time, a.fingerprint
        FROM ai_moniotr_history a
        JOIN alertman_history b ON a.create_time = b.create_time AND a.fingerprint = b.fingerprint
        WHERE a.status = 'suppressed' AND b.status = 'recover'
        AND NOT EXISTS (
            SELECT 1 FROM ai_moniotr_history a2
            WHERE a2.create_time = a.create_time
            AND a2.fingerprint = a.fingerprint
            AND a2.status = 'firing'
        )
        '''
        await cur.execute(query_sql)
        records = await cur.fetchall()

        if not records:
            logger.info("没有需要更新的已恢复告警")
            return 0

        logger.info(f"找到 {len(records)} 个需要更新为recover状态的告警（排除AI曾判断为firing的记录）")

        # 更新记录
        update_count = 0
        for create_time, fingerprint in records:
            # 再次检查当前状态，确保不会覆盖firing状态
            check_sql = '''
            SELECT status FROM ai_moniotr_history
            WHERE create_time = %s AND fingerprint = %s
            ORDER BY create_time DESC
            LIMIT 1
            '''
            await cur.execute(check_sql, (create_time, fingerprint))
            current_result = await cur.fetchone()
            current_status = current_result[0] if current_result else None

            # 如果当前状态是firing，跳过更新
            if current_status == 'firing':
                logger.info(f"跳过更新，AI状态为firing: {fingerprint}")
                continue

            update_sql = '''
            UPDATE ai_moniotr_history
            SET status = 'recover'
            WHERE create_time = %s AND fingerprint = %s AND status = 'suppressed'
            '''
            await cur.execute(update_sql, (create_time, fingerprint))
            affected_rows = cur.rowcount
            update_count += affected_rows

            if affected_rows > 0:
                logger.info(f"更新告警状态为recover: {fingerprint}")

        logger.info(f"共更新 {update_count} 个告警状态为recover")
        return update_count

    except Exception as e:
        logger.error(f"更新已恢复告警状态失败: {e}")
        logger.error(f"异常详情:", exc_info=True)
        return 0


@storage("monitor")
async def check_task_already_processed(db, cur, create_time, fingerprint):
    """
    检查任务是否已经被处理过

    Args:
        create_time: 告警创建时间
        fingerprint: 告警指纹

    Returns:
        bool: True表示已处理，False表示未处理
    """
    try:
        # 检查 ai_moniotr_history 表中是否已有记录
        history_sql = '''
        select count(*) as count
        from ai_moniotr_history
        where create_time = %s and fingerprint = %s
        '''
        await cur.execute(history_sql, (create_time, fingerprint))
        history_result = await cur.fetchone()

        if history_result and history_result[0] > 0:
            logger.debug(f"任务已在 ai_moniotr_history 中找到: {fingerprint}")
            return True

        # 检查 ai_moniotr_recored 表中是否已有记录
        record_sql = '''
        select count(*) as count
        from ai_moniotr_recored
        where create_time = %s and fingerprint = %s
        '''
        await cur.execute(record_sql, (create_time, fingerprint))
        record_result = await cur.fetchone()

        if record_result and record_result[0] > 0:
            logger.debug(f"任务已在 ai_moniotr_recored 中找到: {fingerprint}")
            return True

        logger.debug(f"任务未处理过: {fingerprint}")
        return False

    except Exception as e:
        logger.error(f"检查任务处理状态失败: {e}")
        # 出错时返回False，允许处理
        return False


@storage("monitor")
async def check_ai_whitelist(db, cur, fingerprint: str, alert_data: Dict[str, Any]) -> tuple[bool, str]:
    """
    检查AI白名单规则

    Args:
        fingerprint: 告警指纹
        alert_data: 告警数据，包含title、group等字段

    Returns:
        tuple[bool, str]: (是否在白名单中, 原因说明)
    """
    try:
        title = alert_data.get('title', '')
        group = alert_data.get('group', '')

        logger.info(f"检查AI白名单: fingerprint={fingerprint}, title={title}, group={group}")

        # 一行SQL查询三种白名单规则
        sql = '''
        SELECT rule_id, block_type, content, alert_group
        FROM ai_moniotr_block_alert
        WHERE enable = true
        AND (
            (block_type = 'tg_fingerprint' AND content = %s AND alert_group = %s)
            OR (block_type = 'title' AND content = %s AND alert_group = %s)
            OR (block_type = 'title' AND content = '*' AND alert_group = %s)
        )
        LIMIT 1
        '''
        await cur.execute(sql, (fingerprint, group, title, group, group))
        result = await cur.fetchone()

        if result:
            rule_id, block_type, content, alert_group = result
            logger.info(f"命中AI白名单规则: {rule_id}, type={block_type}, content={content}, group={alert_group}")
            return True, f"命中白名单规则: {rule_id}"
        else:
            logger.debug("未命中任何AI白名单规则")
            return False, "未命中任何白名单规则"

    except Exception as e:
        logger.error(f"检查AI白名单规则失败: {e}")
        # 出错时不在白名单，保持安全
        return False, f"白名单检查异常: {str(e)}"


@storage("monitor")
async def check_ai_block_rules(db, cur, alert_data: Dict[str, Any]) -> bool:
    """
    检查AI阻断规则

    Args:
        alert_data: 告警数据，包含title、group、fingerprint等字段

    Returns:
        bool: True表示应该阻断AI状态，False表示不阻断
    """
    try:
        title = alert_data.get('title', '')
        group = alert_data.get('group', '')
        fingerprint = alert_data.get('fingerprint', '')

        # 查询启用的阻断规则
        sql = '''
        SELECT rule_id, block_type, content
        FROM ai_moniotr_block_alert
        WHERE enable = true
        '''
        await cur.execute(sql)
        rules = await cur.fetchall()

        if not rules:
            logger.debug("没有启用的AI阻断规则")
            return False

        logger.info(f"检查AI阻断规则: title={title}, group={group}, fingerprint={fingerprint}")

        for rule_id, block_type, content in rules:
            logger.debug(f"检查规则 {rule_id}: type={block_type}, content={content}")

            if block_type == 'title':
                # 检查告警标题
                if title == content:
                    logger.info(f"命中title阻断规则: {rule_id}, title={title}")
                    return True

            elif block_type == 'group':
                # 检查告警组
                if group == content:
                    logger.info(f"命中group阻断规则: {rule_id}, group={group}")
                    return True

            elif block_type == 'tg_fingerprint':
                # 检查title#group#fingerprint组合
                tg_fingerprint = f"{title}#{group}#{fingerprint}"
                if tg_fingerprint == content:
                    logger.info(f"命中tg_fingerprint阻断规则: {rule_id}, tg_fingerprint={tg_fingerprint}")
                    return True

        logger.debug("未命中任何AI阻断规则")
        return False

    except Exception as e:
        logger.error(f"检查AI阻断规则失败: {e}")
        # 出错时不阻断，保持原有逻辑
        return False


@storage("monitor")
async def get_ai_block_rules_list(db, cur, alert_group=None):
    """
    获取AI阻断规则列表

    Args:
        alert_group: 可选，告警组筛选条件

    Returns:
        List[Dict]: 阻断规则列表
    """
    try:
        # 构建SQL查询
        if alert_group:
            sql = '''
            SELECT rule_id, block_type, content, enable, update_at, alert_group
            FROM ai_moniotr_block_alert
            WHERE alert_group = %s
            ORDER BY update_at DESC
            '''
            await cur.execute(sql, (alert_group,))
        else:
            sql = '''
            SELECT rule_id, block_type, content, enable, update_at, alert_group
            FROM ai_moniotr_block_alert
            ORDER BY update_at DESC
            '''
            await cur.execute(sql)

        rules = await cur.fetchall()

        result = []
        for rule_id, block_type, content, enable, update_at, alert_group_val in rules:
            result.append({
                'rule_id': rule_id,
                'block_type': block_type,
                'content': content,
                'enable': enable,
                'update_at': update_at.strftime('%Y-%m-%d %H:%M:%S') if update_at else None,
                'alert_group': alert_group_val
            })

        return result

    except Exception as e:
        logger.error(f"获取AI阻断规则列表失败: {e}")
        return []


@storage("monitor")
async def save_ai_block_rule(db, cur, rule_id: str, block_type: str, content: str, enable: bool, alert_group: str):
    """
    保存AI阻断规则

    Args:
        rule_id: 规则ID，如果为"0"则创建新规则
        block_type: 阻断类型 (title/group/tg_fingerprint)
        content: 阻断内容
        enable: 是否启用
        alert_group: 告警组, 必选

    Returns:
        str: 规则ID
    """
    try:
        if rule_id == "0":
            # 创建新规则，使用 alert_group + content 的MD5作为rule_id
            import hashlib
            rule_content = f"{alert_group}#{content}"
            new_rule_id = hashlib.md5(rule_content.encode('utf-8')).hexdigest()

            # 检查规则是否已存在
            check_sql = '''
            SELECT COUNT(*) FROM ai_moniotr_block_alert WHERE rule_id = %s
            '''
            await cur.execute(check_sql, (new_rule_id,))
            exists_result = await cur.fetchone()

            if exists_result and exists_result[0] > 0:
                logger.warning(f"AI阻断规则已存在: rule_id={new_rule_id}, alert_group={alert_group}, content={content}")
                raise ValueError(f"规则已存在，无法创建重复规则")

            sql = '''
            INSERT INTO ai_moniotr_block_alert (rule_id, block_type, content, enable, alert_group, update_at)
            VALUES (%s, %s, %s, %s, %s, NOW())
            '''
            await cur.execute(sql, (new_rule_id, block_type, content, enable, alert_group))
            logger.info(f"创建AI阻断规则: {new_rule_id}, type={block_type}, alert_group={alert_group}, content={content}")
            return new_rule_id
        else:
            # 更新现有规则
            sql = '''
            UPDATE ai_moniotr_block_alert
            SET block_type = %s, content = %s, enable = %s, alert_group = %s, update_at = NOW()
            WHERE rule_id = %s
            '''
            await cur.execute(sql, (block_type, content, enable, alert_group, rule_id))
            logger.info(f"更新AI阻断规则: {rule_id}, type={block_type}, content={content}")
            return rule_id

    except Exception as e:
        logger.error(f"保存AI阻断规则失败: {e}")
        raise


@storage("monitor")
async def get_ai_analysis_status(db, cur, create_time: str, fingerprint: str):
    """
    获取AI分析状态

    Args:
        create_time: 告警创建时间
        fingerprint: 告警指纹

    Returns:
        Dict: AI状态信息，包含status和reason字段
    """
    try:
        sql = '''
        SELECT status, reason, create_time as update_at
        FROM ai_moniotr_history
        WHERE create_time = %s AND fingerprint = %s
        ORDER BY create_time DESC
        LIMIT 1
        '''
        await cur.execute(sql, (create_time, fingerprint))
        result = await cur.fetchone()

        if result:
            status, reason, update_at = result
            return {
                'status': status,
                'reason': reason,
                'update_at': update_at.strftime('%Y-%m-%d %H:%M:%S') if update_at else None
            }

        return None

    except Exception as e:
        logger.error(f"获取AI分析状态失败: {e}")
        return None


async def trigger_background_analysis(create_time: str, fingerprint: str):
    """
    触发后台AI分析

    Args:
        create_time: 告警创建时间
        fingerprint: 告警指纹
    """
    try:
        # 这里可以通过消息队列或者直接调用后台任务
        # 暂时先记录日志，后续实现具体的触发逻辑
        logger.info(f"触发后台AI分析: {create_time}, {fingerprint}")

        # TODO: 实现具体的触发逻辑
        # 可以考虑：
        # 1. 发送消息到队列
        # 2. 直接调用后台分析函数
        # 3. 设置标记让后台任务扫描

    except Exception as e:
        logger.error(f"触发后台AI分析失败: {e}")


@storage("monitor")
async def get_pending_analysis_alerts(db, cur, limit: int = 100):
    """
    获取需要AI分析的告警列表

    Args:
        limit: 限制返回数量

    Returns:
        List[Dict]: 待分析的告警列表
    """
    try:
        # 查询最近的告警，但AI状态不为recover或firing的
        sql = '''
        SELECT DISTINCT a.create_time, a.fingerprint, a.psm, a.title, a.level, a.content, a.`group`
        FROM alertman_history a
        LEFT JOIN ai_moniotr_history h ON a.create_time = h.create_time AND a.fingerprint = h.fingerprint
        WHERE a.create_time >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
        AND a.status = 'firing'
        AND (h.status IS NULL OR h.status NOT IN ('recover', 'firing'))
        ORDER BY a.create_time DESC
        LIMIT %s
        '''
        await cur.execute(sql, (limit,))
        results = await cur.fetchall()

        alerts = []
        for row in results:
            create_time, fingerprint, psm, title, level, content, group = row
            alerts.append({
                'create_time': create_time,
                'fingerprint': fingerprint,
                'psm': psm,
                'title': title,
                'level': level,
                'content': content,
                'group': group or 'unknown'
            })

        return alerts

    except Exception as e:
        logger.error(f"获取待分析告警列表失败: {e}")
        return []





@storage("monitor")
async def check_ai_analysis_queue_status(db, cur, create_time: str, fingerprint: str) -> tuple[bool, str]:
    """
    检查AI分析队列状态，判断是否需要兜底返回firing

    Args:
        create_time: 告警创建时间
        fingerprint: 告警指纹

    Returns:
        tuple: (is_queue_blocked, reason)
        - is_queue_blocked: True表示队列可能阻塞，需要返回firing
        - reason: 判断原因
    """
    try:
        # 1. 查询AI状态记录
        status_sql = '''
        SELECT status, reason, create_time as update_at
        FROM ai_moniotr_history
        WHERE create_time = %s AND fingerprint = %s
        ORDER BY create_time DESC
        LIMIT 1
        '''
        await cur.execute(status_sql, (create_time, fingerprint))
        status_result = await cur.fetchone()

        if not status_result:
            # 没有AI状态记录，不算队列阻塞
            return False, "无AI状态记录"

        status, reason, update_at = status_result

        # 2. 只检查suppressed状态
        if status != 'suppressed':
            return False, f"AI状态为{status}，非suppressed"

        # 3. 检查更新时间
        if not update_at:
            logger.warning(f"AI状态suppressed但无更新时间: {fingerprint}")
            return True, "AI状态suppressed但无更新时间"

        # 4. 计算时间差
        now = datetime.now()
        time_diff_minutes = (now - update_at).total_seconds() / 60

        # 5. 判断是否超过3分钟
        if time_diff_minutes > 3:
            logger.warning(f"AI分析队列可能阻塞: {fingerprint}, 状态: {status}, 上次更新: {update_at}, 间隔: {time_diff_minutes:.1f}分钟")
            return True, f"AI分析队列过长，上次更新{time_diff_minutes:.1f}分钟前"

        # 6. 时间正常
        logger.debug(f"AI分析队列正常: {fingerprint}, 间隔: {time_diff_minutes:.1f}分钟")
        return False, f"AI分析队列正常，{time_diff_minutes:.1f}分钟前更新"

    except Exception as e:
        logger.error(f"检查AI分析队列状态失败: {fingerprint}, 错误: {e}")
        # 出错时保守返回True，确保告警不被遗漏
        return True, f"检查AI分析队列状态异常: {str(e)}"


@storage("monitor")
async def get_ai_analysis_queue_stats(db, cur) -> dict:
    """
    获取AI分析队列统计信息

    Returns:
        dict: 队列统计信息
    """
    try:
        # 统计各状态的告警数量
        stats_sql = '''
        SELECT
            status,
            COUNT(*) as count,
            MIN(create_time) as oldest_time,
            MAX(create_time) as newest_time
        FROM ai_moniotr_history
        WHERE create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        GROUP BY status
        '''
        await cur.execute(stats_sql)
        results = await cur.fetchall()

        stats = {
            'total': 0,
            'by_status': {},
            'oldest_time': None,
            'newest_time': None
        }

        for status, count, oldest_time, newest_time in results:
            stats['total'] += count
            stats['by_status'][status] = {
                'count': count,
                'oldest_time': oldest_time.strftime('%Y-%m-%d %H:%M:%S') if oldest_time else None,
                'newest_time': newest_time.strftime('%Y-%m-%d %H:%M:%S') if newest_time else None
            }

            # 更新全局时间范围
            if not stats['oldest_time'] or (oldest_time and oldest_time < datetime.strptime(stats['oldest_time'], '%Y-%m-%d %H:%M:%S')):
                stats['oldest_time'] = oldest_time.strftime('%Y-%m-%d %H:%M:%S') if oldest_time else None
            if not stats['newest_time'] or (newest_time and newest_time > datetime.strptime(stats['newest_time'], '%Y-%m-%d %H:%M:%S')):
                stats['newest_time'] = newest_time.strftime('%Y-%m-%d %H:%M:%S') if newest_time else None

        # 检查可能阻塞的告警
        blocked_sql = '''
        SELECT COUNT(*) as blocked_count
        FROM ai_moniotr_history
        WHERE status = 'suppressed'
        AND create_time < DATE_SUB(NOW(), INTERVAL 3 MINUTE)
        AND create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        '''
        await cur.execute(blocked_sql)
        blocked_result = await cur.fetchone()
        stats['potentially_blocked'] = blocked_result[0] if blocked_result else 0

        return stats

    except Exception as e:
        logger.error(f"获取AI分析队列统计失败: {e}")
        return {
            'total': 0,
            'by_status': {},
            'potentially_blocked': 0,
            'error': str(e)
        }


@storage("monitor")
async def check_ai_result_exists(db, cur, create_time: str, fingerprint: str) -> bool:
    """
    检查AI分析结果是否已经存在

    Args:
        create_time: 创建时间
        fingerprint: 指纹

    Returns:
        bool: True表示已存在，False表示不存在
    """
    try:
        # 检查ai_moniotr_history表中是否有记录
        sql = '''
        SELECT COUNT(*) as count
        FROM ai_moniotr_history
        WHERE create_time = %s AND fingerprint = %s
        '''
        await cur.execute(sql, (create_time, fingerprint))
        result = await cur.fetchone()

        exists = result[0] > 0 if result else False
        logger.debug(f"检查AI结果是否存在: {fingerprint} -> {exists}")

        return exists

    except Exception as e:
        logger.error(f"检查AI结果是否存在失败: {fingerprint}, 错误: {e}")
        # 出错时保守返回True，避免重复推送
        return True