import json
import logging
from datetime import datetime
from typing import Dict, Any, Tuple
from .utils import save_ai_monitor_history

logger = logging.getLogger(__name__)


async def check_alert_duration(content: Dict[str, Any]) -> float:
    """
    检查告警持续时间

    Args:
        content: 告警内容字典

    Returns:
        float: 告警持续时间（分钟）
    """
    try:
        # 从告警内容中获取开始时间
        # 通常告警内容中会有 startsAt 字段
        starts_at = content.get("startsAt")
        if not starts_at:
            # 如果没有 startsAt，尝试其他可能的字段
            starts_at = content.get("start_time") or content.get("alertTime")

        if not starts_at:
            logger.warning("无法从告警内容中获取开始时间")
            return 0.0

        # 解析时间字符串
        if isinstance(starts_at, str):
            try:
                # 尝试解析 ISO 格式时间
                if 'T' in starts_at:
                    start_time = datetime.fromisoformat(starts_at.replace('Z', '+00:00'))
                else:
                    # 尝试解析其他格式
                    start_time = datetime.strptime(starts_at, '%Y-%m-%d %H:%M:%S')
            except ValueError as e:
                logger.error(f"时间格式解析失败: {starts_at}, 错误: {e}")
                return 0.0
        else:
            logger.warning(f"告警开始时间格式不正确: {starts_at}")
            return 0.0

        # 计算持续时间
        current_time = datetime.now(start_time.tzinfo) if start_time.tzinfo else datetime.now()
        duration = current_time - start_time
        duration_minutes = duration.total_seconds() / 60

        logger.info(f"告警持续时间: {duration_minutes:.2f} 分钟")
        return duration_minutes

    except Exception as e:
        logger.error(f"检查告警持续时间时发生异常: {e}")
        return 0.0


async def process_alert_core_logic(alert_data: Dict[str, Any]) -> Tuple[str, str, str]:
    """
    处理告警的核心逻辑

    Args:
        alert_data: 包含告警信息的字典，包含 psm, title, level, content 等字段

    Returns:
        Tuple[str, str, str]: (data_status, message, reason)
        - data_status: "firing" 或 "suppressed"
        - message: 处理结果消息
        - reason: 判断理由（业务强依赖/时间阈值兜底逻辑/AI分析判断等）
    """
    try:
        psm = alert_data.get('psm')
        title = alert_data.get('title')
        level = alert_data.get('level')
        content_str = alert_data.get('content', '{}')

        # 解析content JSON
        try:
            content = json.loads(content_str)
        except json.JSONDecodeError:
            logger.error(f"告警content解析失败: {content_str}")
            return "firing", "告警内容格式错误", "数据格式错误"

        # 1. 代码逻辑兜底分析 - 检查告警持续时间
        from ...config import base
        threshold_minutes = base.get("ALERT_DURATION_THRESHOLD_MINUTES", 10)

        alert_duration_minutes = await check_alert_duration(content)
        if alert_duration_minutes >= threshold_minutes:
            logger.info(f"告警持续时间 {alert_duration_minutes} 分钟，超过{threshold_minutes}分钟阈值")
            return "firing", f"{threshold_minutes} 分钟告警还没恢复", f"{threshold_minutes}分钟兜底逻辑"

        # 1.1 检查是否超过20分钟 - 跳过AI图形分析
        if alert_duration_minutes >= 20:
            logger.info(f"告警持续时间 {alert_duration_minutes} 分钟，超过20分钟，跳过AI图形分析")
            return "firing", "告警持续超过20分钟，跳过AI分析", "20分钟时间限制"

        # 2. 检查是否开启AI盯盘（暂时关闭此逻辑）
        # labels = content.get("labels", {})
        # enable_ai_monitor = labels.get("enable_ai_monitor", 0)
        # if enable_ai_monitor != 1:
        #     logger.info(f"PSM {psm} 未开启AI盯盘")
        #     return "firing", "未打开 ai 盯盘"

        # 2. 判断告警类型 - 直接使用handler_map验证
        labels = content.get("labels", {})
        alert_source = labels.get("alert_source", "")

        if alert_source in handler_map:
                return alert_source, f"匹配 {alert_source} 类型", "告警类型匹配"

        # 如果不匹配任何已知类型
        logger.warning(f"未知的告警类型: alert_source={alert_source}, title={title}")
        return "firing", "未知的告警类型", "未知告警类型"

    except Exception as e:
        logger.error(f"处理告警核心逻辑时发生异常: {e}")
        return "firing", "未知错误无法提供 AI 盯盘能力", "系统异常"


async def determine_alert_type(alert_data: Dict[str, Any]) -> str:
    """
    根据告警数据确定告警类型 - 使用handler_map验证

    Args:
        alert_data: 告警数据

    Returns:
        str: 告警类型 ("argos" 等)
    """
    try:
        content = json.loads(alert_data.get('content', '{}'))
        labels = content.get("labels", {})
        alert_source = labels.get("alert_source", "")
        title = alert_data.get('title', "")

        # 遍历handler_map查找匹配的类型
        for alert_type, config in handler_map.items():
            # 检查alert_source是否匹配
            if alert_source == alert_type:
                # 检查title是否在允许列表中
                title_allows = config.get("title_allows", set())
                if not title_allows or title in title_allows:
                    return alert_type

        return "unknown"

    except Exception as e:
        logger.error(f"确定告警类型时发生异常: {e}")
        return "unknown"


async def save_monitor_history(create_time: str, fingerprint: str, status: str, alert_data: Dict[str, Any], reason: str = ""):
    """保存AI监控历史记录"""
    try:
        content = json.loads(alert_data.get('content', '{}'))
        labels = content.get("labels", {})

        await save_ai_monitor_history(
            create_time=create_time,
            fingerprint=fingerprint,
            status=status,
            alertname=labels.get("alertname", ""),
            alert_source=labels.get("alert_source", ""),
            psm=alert_data.get('psm', ""),
            level=alert_data.get('level', ""),
            title=alert_data.get('title', ""),
            group=labels.get("group", ""),
            content=alert_data.get('content', ""),
            reason=reason
        )
        logger.info(f"保存AI监控历史记录成功: {fingerprint}, 判断理由: {reason}")
    except Exception as e:
        logger.error(f"保存AI监控历史记录失败: {e}")


def validate_alert_data(alert_data: Dict[str, Any]) -> bool:
    """验证告警数据的完整性"""
    required_fields = ['psm', 'title', 'level', 'content']
    for field in required_fields:
        if field not in alert_data or not alert_data[field]:
            logger.error(f"告警数据缺少必要字段: {field}")
            return False
    return True


async def process_alert_with_ai_analysis(
    alert_data: Dict[str, Any],
    create_time: str,
    fingerprint: str,
    analysis_type: str = "通用"
) -> Tuple[str, str, str]:
    """
    通用的AI告警分析处理流程

    注意：此函数只有在兜底逻辑检查通过后才会被调用
    即：告警持续时间 < 配置阈值 且 不是业务强依赖

    Args:
        alert_data: 告警数据
        create_time: 创建时间
        fingerprint: 告警指纹
        analysis_type: 分析类型，用于日志和错误消息

    Returns:
        Tuple[str, str, str]: (data_status, message, reason)
    """
    try:
        from .utils import format_graph_base64, get_ai_response, save_chart_record, check_business_strong_dependency

        # 1. 检查业务强依赖状态（用于AI分析参考）
        psm = alert_data.get('psm', '')
        is_strong_dependency, f_fingerprints = await check_business_strong_dependency(psm)

        if is_strong_dependency and len(f_fingerprints) > 0:
            logger.info(f"PSM {psm} 是业务强依赖，AI分析时会考虑此因素")
            dependency_context = "此服务是业务强依赖，需要特别关注"
        else:
            dependency_context = "此服务不是业务强依赖"

        # 2. 生成监控图表并获取原始URL和source
        from .utils import get_graph_image_data_with_url
        image_data, chart_url, graph_source = await get_graph_image_data_with_url(fingerprint)

        # 3. 转换为base64
        base64_image = await format_graph_base64(image_data)

        # 4. 调用AI分析（传入create_time、业务依赖上下文和图形源）
        ai_result = await get_ai_response(base64_image, create_time, dependency_context, graph_source)

        # 4. 保存图表记录到ai_moniotr_recored表（包含AI分析结果）
        try:
            # 使用真实的URL，如果为空则使用描述
            url_to_save = chart_url if chart_url else f"{analysis_type}图表生成"
            await save_chart_record(create_time, fingerprint, url_to_save, base64_image, ai_result)
            logger.info(f"保存图表记录成功: {fingerprint}, URL: {url_to_save}")

            # 5. 推送AI结果到lark_robot接口
            try:
                from ...utils import push_ai_result_with_picture
                ai_description = ai_result.get("response", "")
                if ai_description != "":
                    success, message = await push_ai_result_with_picture(
                        create_time=create_time,
                        fingerprint=fingerprint,
                        base64_image=base64_image,
                        ai_result=ai_description
                    )
                    if success:
                        logger.info(f"推送AI结果成功: {fingerprint}, {message}")
                    else:
                        logger.warning(f"推送AI结果失败: {fingerprint}, {message}")
            except Exception as push_error:
                logger.error(f"推送AI结果异常: {fingerprint}, 错误: {push_error}")

        except Exception as save_error:
            logger.error(f"保存图表记录失败: {save_error}")

        # 6. 获取AI分析结果
        ai_status = ai_result.get("status", "firing")

        final_reason = "AI分析判断"
        # if is_strong_dependency and len(f_fingerprints) > 0:
        #     if ai_status == "suppressed":
        #         # 业务强依赖但AI判断可抑制，需要更谨慎
        #         logger.info(f"业务强依赖服务AI判断为suppressed，保持谨慎态度")
        #         final_reason = "AI分析判断(业务强依赖)"
        #     else:
        #         # 业务强依赖且AI判断需要处理
        #         final_reason = "AI分析判断(业务强依赖)"

        # 7. 保存监控历史
        await save_monitor_history(create_time, fingerprint, ai_status, alert_data, final_reason)

        # 8. 返回结果
        if ai_status == "suppressed":
            return "suppressed", f"AI{analysis_type}分析判断告警可抑制", final_reason
        else:
            return "firing", f"AI{analysis_type}分析判断告警需要处理", final_reason

    except Exception as e:
        logger.error(f"处理{analysis_type}告警失败: {e}")
        # 保存失败记录
        try:
            await save_monitor_history(create_time, fingerprint, "firing", alert_data, "AI分析异常")
        except:
            pass
        return "firing", "AI 多模态图片趋势分析失败", "AI分析异常"


async def argos(alert_data: Dict[str, Any], create_time: str, fingerprint: str) -> Tuple[str, str, str]:
    """
    处理argos类型的告警

    Args:
        alert_data: 告警数据
        create_time: 创建时间
        fingerprint: 告警指纹

    Returns:
        Tuple[str, str, str]: (data_status, message, reason)
    """
    try:
        # 使用通用的AI分析流程
        return await process_alert_with_ai_analysis(
            alert_data, create_time, fingerprint, "argos"
        )
    except Exception as e:
        logger.error(f"处理argos告警失败: {e}")
        return "firing", "AI 多模态图片趋势分析失败", "argos处理异常"
    
async def kepler(alert_data: Dict[str, Any], create_time: str, fingerprint: str) -> Tuple[str, str, str]:
    """
    处理kepler类型的告警

    Args:
        alert_data: 告警数据
        create_time: 创建时间
        fingerprint: 告警指纹

    Returns:
        Tuple[str, str, str]: (data_status, message, reason)
    """
    try:
        # 使用通用的AI分析流程
        return await process_alert_with_ai_analysis(
            alert_data, create_time, fingerprint, "kepler"
        )
    except Exception as e:
        logger.error(f"处理kepler告警失败: {e}")
        return "firing", "AI 多模态图片趋势分析失败", "kepler处理异常"


async def common(alert_data: Dict[str, Any], create_time: str, fingerprint: str) -> Tuple[str, str, str]:
    """
    处理common类型的告警

    Args:
        alert_data: 告警数据
        create_time: 创建时间
        fingerprint: 告警指纹

    Returns:
        Tuple[str, str, str]: (data_status, message, reason)
    """
    try:
        # 使用通用的AI分析流程
        return await process_alert_with_ai_analysis(
            alert_data, create_time, fingerprint, "common"
        )
    except Exception as e:
        logger.error(f"处理common告警失败: {e}")
        return "firing", "AI 多模态图片趋势分析失败", "common处理异常"


handler_map = {
    "argos": {
        "func": argos,
    },
    "kepler": {
        "func": kepler,
    },
    "common": {
        "func": common,
    }
}