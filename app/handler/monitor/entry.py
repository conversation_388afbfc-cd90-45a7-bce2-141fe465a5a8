"""
AI 盯盘系统入口逻辑
负责协调整个告警处理流程
"""

import json
import logging
from typing import Dict, Any, <PERSON><PERSON>

from .utils import get_alert_item, trigger_background_analysis
from .handler import process_alert_core_logic, determine_alert_type, handler_map

logger = logging.getLogger(__name__)


async def process_alert_with_data(create_time: str, fingerprint: str, alert_data: Dict[str, Any]) -> Tuple[str, str, bool]:
    """
    直接使用告警数据处理告警检查

    Args:
        create_time: 告警创建时间
        fingerprint: 告警指纹
        alert_data: 告警数据字典，包含 psm, title, level, content

    Returns:
        Tuple[str, str, bool]: (data_status, message, success)
        - data_status: "firing" 或 "suppressed"
        - message: 处理结果消息
        - success: 是否成功处理
    """
    try:
        # 1. 参数验证
        if not create_time or not fingerprint:
            return "firing", "缺少必要参数 create_time 或 fingerprint", False

        if not alert_data or not all(key in alert_data for key in ['psm', 'title', 'level', 'content']):
            return "firing", "告警数据不完整", False

        # 2. 检查当前盯盘任务数量
        from .utils import get_ai_monitor_history_status, get_active_monitoring_tasks_count
        active_tasks_count = await get_active_monitoring_tasks_count()

        logger.info(f"当前活跃盯盘任务数量: {active_tasks_count}")

        if active_tasks_count >= 30:
            logger.warning(f"当前盯盘任务数量 {active_tasks_count} 超过限制 30，直接返回 firing")
            return "firing", f"当前盯盘任务过多({active_tasks_count}个)，暂时无法处理新任务", True

        # 3. 检查历史记录状态
        history_status = await get_ai_monitor_history_status(create_time, fingerprint)

        if history_status == "firing":
            logger.info(f"历史记录显示告警 {fingerprint} 状态为 firing，直接返回")
            return "firing", "历史记录显示已经是需要马上升级", True

        # 暂时不要 4. 检查PSM是否为业务场景的强依赖（仅在check接口中）
        # from .utils import check_business_strong_dependency
        # psm = alert_data.get('psm')
        # is_strong_dependency, f_fingerprints = await check_business_strong_dependency(psm)
        # if is_strong_dependency and len(f_fingerprints) > 0:
        #     logger.info(f"PSM {psm} 是业务场景的强依赖，直接返回firing")
        #     # 保存监控历史记录
        #     from .handler import save_monitor_history
        #     await save_monitor_history(create_time, fingerprint, "firing", alert_data, "业务强依赖")
        #     return "firing", "业务强依赖，需要立即处理", True

        # 3. 执行核心业务逻辑检查（时间阈值等）
        data_status, message, reason = await process_alert_core_logic(alert_data)

        # 如果核心逻辑返回firing（时间阈值），则不检查AI阻断规则
        if data_status == "firing":
            logger.info(f"核心逻辑返回firing，跳过AI阻断检查: {fingerprint}, 原因: {reason}")
            # 保存监控历史记录
            from .handler import save_monitor_history
            await save_monitor_history(create_time, fingerprint, data_status, alert_data, reason)
            return data_status, message, True

        # 4. 检查AI阻断规则（仅在核心逻辑未返回firing时）
        from .utils import check_ai_block_rules
        alert_data_with_fingerprint = {**alert_data, 'fingerprint': fingerprint}
        is_blocked = await check_ai_block_rules(alert_data_with_fingerprint)

        if is_blocked:
            logger.info(f"命中AI阻断规则，跳过AI分析: {fingerprint}")
            # 保存监控历史记录
            from .handler import save_monitor_history
            await save_monitor_history(create_time, fingerprint, "suppressed", alert_data, "AI阻断规则")
            return "suppressed", "命中AI阻断规则", True

        if data_status == "suppressed":
            # 保存监控历史记录
            from .handler import save_monitor_history
            await save_monitor_history(create_time, fingerprint, data_status, alert_data, reason)
            return data_status, message, True

        # 5. 确定告警类型并调用对应处理器
        alert_type = await determine_alert_type(alert_data)

        if alert_type == "unknown":
            return "firing", "未知的告警类型", True

        # 6. 获取处理器配置
        handler_config = handler_map.get(alert_type)
        if not handler_config:
            # 保存失败记录
            from .handler import save_monitor_history
            await save_monitor_history(create_time, fingerprint, "firing", alert_data, "处理器配置错误")
            return "firing", f"未找到 {alert_type} 类型的处理器", True

        handler_func = handler_config.get("func")
        if not handler_func:
            # 保存失败记录
            from .handler import save_monitor_history
            await save_monitor_history(create_time, fingerprint, "firing", alert_data, "处理器配置错误")
            return "firing", f"{alert_type} 类型的处理器配置错误", True

        # 7. 执行具体的告警处理
        final_status, final_message, final_reason = await handler_func(alert_data, create_time, fingerprint)
            # 保存AI分析结果到历史记录（用于数据收集）
        from .handler import save_monitor_history
        await save_monitor_history(create_time, fingerprint, final_status, alert_data, f"AI分析结果: {final_reason}")

        return final_status, final_message, True

    except Exception as e:
        logger.error(f"处理告警检查时发生异常: {e}")
        return "firing", "未知错误无法提供 AI 盯盘能力", False


async def process_alert_check(create_time: str, fingerprint: str) -> Tuple[str, str, bool]:
    """
    处理告警检查请求 - 仅读取AI状态

    Args:
        create_time: 告警创建时间
        fingerprint: 告警指纹

    Returns:
        Tuple[str, str, bool]: (data_status, message, success)
        - data_status: "firing", "suppressed", "recover" 等
        - message: 状态描述消息
        - success: 是否成功查询
    """
    try:
        # 1. 参数验证
        if not create_time or not fingerprint:
            return "firing", "缺少必要参数 create_time 或 fingerprint", False

        # 2. 先查询告警数据
        from .utils import get_alert_item, check_business_strong_dependency
        alert_item = await get_alert_item(create_time, fingerprint)

        # 构建alert_data用于白名单检查
        alert_data = {}
        if alert_item:
            psm, title, level, content, group = alert_item[0]
            alert_data = {
                'psm': psm,
                'title': title,
                'level': level,
                'content': content,
                'fingerprint': fingerprint,
                'alert_data': group,
            }

        # 3. 检查白名单（ai_moniotr_block_alert表）
        from .utils import check_ai_whitelist
        is_whitelisted, whitelist_reason = await check_ai_whitelist(fingerprint, alert_data)

        if not is_whitelisted:
            logger.info(f"告警未在白名单中: {fingerprint}, 原因: {whitelist_reason}")
            # 保存监控历史记录
            from .handler import save_monitor_history
            await save_monitor_history(create_time, fingerprint, "firing", alert_data, whitelist_reason)
            return "firing", "该告警没开白名单", True

        # 4. 检查是否为业务强依赖
        if alert_item:
            psm = alert_item[0][0]
            if psm:
                is_strong_dependency, f_fingerprints = await check_business_strong_dependency(psm)
                if is_strong_dependency and len(f_fingerprints) > 0:
                    logger.info(f"PSM {psm} 是业务场景的强依赖，直接返回firing")
                    return "firing", "业务强依赖，需要立即处理", True

        # 5. 查询AI分析状态
        from .utils import get_ai_analysis_status
        ai_status = await get_ai_analysis_status(create_time, fingerprint)

        if ai_status:
            status = ai_status.get('status', 'firing')
            reason = ai_status.get('reason', 'AI分析失败')

            # 6. 判断AI状态在抑制中，但分析时间超过3分钟的情况（兜底逻辑）
            from .utils import check_ai_analysis_queue_status
            is_queue_blocked, queue_reason = await check_ai_analysis_queue_status(create_time, fingerprint)

            if is_queue_blocked:
                logger.warning(f"AI分析队列可能阻塞，兜底返回firing: {fingerprint}, 原因: {queue_reason}")
                return "firing", f"AI分析队列过长，兜底返回firing", True

            logger.info(f"查询AI状态: {fingerprint} -> {status} ({reason})")
            return status, f"AI状态: {reason}", True
        else:
            # 如果没有AI状态记录，触发后台分析并返回默认状态
            logger.info(f"未找到AI状态记录，触发后台分析: {fingerprint}")
            # await trigger_background_analysis(create_time, fingerprint)
            return "firing", "AI分析中，请稍后查询", True

    except Exception as e:
        logger.error(f"查询AI状态失败: {e}")
        logger.error(f"异常详情:", exc_info=True)
        return "firing", "查询AI状态异常", False


def validate_request_params(post_data: Dict[str, Any]) -> Tuple[str, str, str]:
    """
    验证请求参数
    
    Args:
        post_data: 请求数据
        
    Returns:
        Tuple[str, str, str]: (create_time, fingerprint, error_message)
        如果验证失败，error_message 不为空
    """
    if not post_data:
        return "", "", "请求参数为空"

    create_time = post_data.get("create_time")
    fingerprint = post_data.get("fingerprint")

    if not create_time or not fingerprint:
        return "", "", "缺少必要参数 create_time 或 fingerprint"

    return create_time, fingerprint, ""