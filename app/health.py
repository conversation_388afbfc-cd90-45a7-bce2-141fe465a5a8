"""
健康检查模块
提供应用健康状态检查功能
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Any

from sanic import Blueprint
from sanic.response import json

logger = logging.getLogger(__name__)

health = Blueprint("health", url_prefix="/")

# 应用启动时间
APP_START_TIME = time.time()

# 健康检查状态
HEALTH_STATUS = {
    "database": True,
    "ai_model": True,
    "external_services": True,
    "last_check": datetime.now().isoformat()
}


async def check_database_health() -> bool:
    """检查数据库连接健康状态"""
    try:
        from .utils import storage
        
        @storage("monitor")
        async def test_db_connection(db, cur):
            await cur.execute("SELECT 1")
            result = await cur.fetchone()
            return result is not None
        
        result = await test_db_connection()
        return result
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return False


async def check_ai_model_health() -> bool:
    """检查AI模型服务健康状态"""
    try:
        return True
    except Exception as e:
        logger.error(f"AI模型健康检查失败: {e}")
        return False


async def check_external_services_health() -> bool:
    """检查外部服务健康状态"""
    try:
        return True
    except Exception as e:
        logger.error(f"外部服务健康检查失败: {e}")
        return False


async def update_health_status():
    """更新健康检查状态"""
    global HEALTH_STATUS
    
    try:
        # 并发检查各个组件
        db_task = asyncio.create_task(check_database_health())
        ai_task = asyncio.create_task(check_ai_model_health())
        external_task = asyncio.create_task(check_external_services_health())
        
        # 等待所有检查完成，但设置超时
        db_health, ai_health, external_health = await asyncio.wait_for(
            asyncio.gather(db_task, ai_task, external_task, return_exceptions=True),
            timeout=30
        )
        
        # 处理异常结果
        if isinstance(db_health, Exception):
            db_health = False
        if isinstance(ai_health, Exception):
            ai_health = False
        if isinstance(external_health, Exception):
            external_health = False
        
        HEALTH_STATUS.update({
            "database": db_health,
            "ai_model": ai_health,
            "external_services": external_health,
            "last_check": datetime.now().isoformat()
        })
        
    except asyncio.TimeoutError:
        logger.error("健康检查超时")
        HEALTH_STATUS.update({
            "database": False,
            "ai_model": False,
            "external_services": False,
            "last_check": datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"健康检查更新失败: {e}")


"""
健康检查 curl 测试命令:

# 基础健康检查
curl http://localhost/api/v1/ai-monitor/health

# 详细健康检查 (带格式化)
curl http://localhost/api/v1/ai-monitor/health | python3 -m json.tool

# 就绪状态检查
curl http://localhost/api/v1/ai-monitor/ready

# 指标信息
curl http://localhost/api/v1/ai-monitor/metrics

# 版本信息
curl http://localhost/api/v1/ai-monitor/version

# K8s 环境健康检查
curl http://<node-ip>:30320/health
curl http://<node-ip>:30320/ready

# 健康检查状态码测试
curl -w "HTTP Status: %{http_code}\n" -o /dev/null -s http://localhost/api/v1/ai-monitor/health

# 持续监控健康状态
watch -n 5 'curl -s http://localhost/api/v1/ai-monitor/health | python3 -m json.tool'

# 所有健康相关端点测试
for endpoint in health ready metrics version; do
  echo "=== Testing /$endpoint ==="
  curl -s http://localhost/api/v1/ai-monitor/$endpoint | python3 -m json.tool
  echo
done

# 一键健康检查脚本
#!/bin/bash
BASE_URL=${1:-"http://localhost:8000"}
echo "🏥 健康检查测试 - $BASE_URL"

echo "✅ 健康状态:" && curl -s "$BASE_URL/health" | python3 -m json.tool
echo "🚀 就绪状态:" && curl -s "$BASE_URL/ready" | python3 -m json.tool
echo "📊 运行指标:" && curl -s "$BASE_URL/metrics" | python3 -m json.tool
echo "🏷️  版本信息:" && curl -s "$BASE_URL/version" | python3 -m json.tool

"""

@health.route('/health', methods=["GET"])
async def health_check(request):
    """
    健康检查端点 - 用于 livenessProbe
    返回应用的整体健康状态

    响应格式:
        {
            "status": "healthy|unhealthy",
            "timestamp": "2024-01-01T12:00:00Z",
            "uptime": {
                "seconds": 3600,
                "minutes": 60,
                "hours": 1,
                "formatted": "1h 0m 0s"
            },
            "components": {
                "database": true,
                "ai_model": true,
                "external_services": true,
                "last_check": "2024-01-01T12:00:00Z"
            },
            "version": "1.0.0",
            "environment": "production"
        }

    HTTP状态码:
        200: 健康
        503: 不健康
        500: 检查失败
    """
    try:
        # 更新健康状态
        await update_health_status()
        
        # 计算运行时间
        uptime_seconds = time.time() - APP_START_TIME
        uptime_minutes = int(uptime_seconds // 60)
        uptime_hours = int(uptime_minutes // 60)
        
        # 检查关键组件状态
        is_healthy = HEALTH_STATUS["database"] and HEALTH_STATUS["ai_model"]
        
        response_data = {
            "status": "healthy" if is_healthy else "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "uptime": {
                "seconds": int(uptime_seconds),
                "minutes": uptime_minutes,
                "hours": uptime_hours,
                "formatted": f"{uptime_hours}h {uptime_minutes % 60}m {int(uptime_seconds % 60)}s"
            },
            "components": HEALTH_STATUS,
            "version": "1.0.0",
            "environment": "production"
        }
        
        # 如果不健康，返回503状态码
        status_code = 200 if is_healthy else 503
        
        return json(response_data, status=status_code)
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return json({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }, status=500)


"""
就绪检查 curl 测试命令:

# 基础就绪检查
curl http://localhost/api/v1/ai-monitor/ready

# 检查就绪状态码
curl -w "Ready Status: %{http_code}\n" -o /dev/null -s http://localhost/api/v1/ai-monitor/ready

# K8s 就绪检查
curl http://<node-ip>:30320/ready

# 就绪检查详情
curl -s http://localhost/api/v1/ai-monitor/ready | python3 -m json.tool
"""

@health.route('/ready', methods=["GET"])
async def readiness_check(request):
    """
    就绪检查端点 - 用于 readinessProbe
    检查应用是否准备好接收流量

    响应格式:
        {
            "status": "ready|not_ready",
            "timestamp": "2024-01-01T12:00:00Z",
            "checks": {
                "database": true,
                "startup_complete": true
            }
        }

    HTTP状态码:
        200: 就绪
        503: 未就绪
        500: 检查失败
    """
    try:
        # 检查关键组件是否就绪
        await update_health_status()
        
        # 就绪条件：数据库连接正常
        is_ready = HEALTH_STATUS["database"]
        
        response_data = {
            "status": "ready" if is_ready else "not_ready",
            "timestamp": datetime.now().isoformat(),
            "checks": {
                "database": HEALTH_STATUS["database"],
                "startup_complete": time.time() - APP_START_TIME > 30  # 启动超过30秒
            }
        }
        
        status_code = 200 if is_ready else 503
        
        return json(response_data, status=status_code)
        
    except Exception as e:
        logger.error(f"就绪检查失败: {e}")
        return json({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }, status=500)


@health.route('/metrics', methods=["GET"])
async def metrics_endpoint(request):
    """
    指标端点 - 提供应用运行指标
    """
    try:
        # 更新健康状态
        await update_health_status()
        
        # 计算运行时间
        uptime_seconds = time.time() - APP_START_TIME
        
        # 模拟一些业务指标
        metrics_data = {
            "timestamp": datetime.now().isoformat(),
            "uptime_seconds": int(uptime_seconds),
            "health_status": HEALTH_STATUS,
            "system": {
                "cpu_usage": "N/A",  # 可以集成psutil获取实际数据
                "memory_usage": "N/A",
                "disk_usage": "N/A"
            },
            "business": {
                "total_alerts_processed": "N/A",  # 可以从数据库查询
                "ai_analysis_count": "N/A",
                "avg_response_time": "N/A"
            },
            "background_tasks": {}
        }

        # 获取后台任务统计
        try:
            from .tasks import background_stats
            metrics_data["background_tasks"] = background_stats.get_stats()
        except Exception as e:
            logger.warning(f"获取后台任务统计失败: {e}")
            metrics_data["background_tasks"] = {"error": "统计信息不可用"}
        
        return json(metrics_data)
        
    except Exception as e:
        logger.error(f"指标获取失败: {e}")
        return json({
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }, status=500)


@health.route('/version', methods=["GET"])
async def version_info(request):
    """
    版本信息端点
    """
    return json({
        "name": "AI Monitors",
        "version": "1.0.0",
        "build_time": "2024-01-01T00:00:00Z",
        "git_commit": "unknown",
        "python_version": "3.9+",
        "dependencies": {
            "sanic": "21.9.3",
            "httpx": "0.22.0",
            "matplotlib": "3.7.1"
        }
    })


# 定期健康检查任务
async def periodic_health_check():
    """定期更新健康状态的后台任务"""
    while True:
        try:
            await update_health_status()
            await asyncio.sleep(60)  # 每分钟检查一次
        except Exception as e:
            logger.error(f"定期健康检查失败: {e}")
            await asyncio.sleep(60)
