from urllib.parse import urlencode
from urllib.parse import urlunparse
import traceback
import logging

import datetime
import binascii
import time
import json as cjson

from sanic import Blueprint
from sanic.response import text, html, json
import requests
from datetime import timedelta, datetime
import collections
import asyncio
from .handler.monitor.entry import validate_request_params, process_alert_check
from .utils import format_response
# 主要逻辑已移至 monitor/entry.py

logger = logging.getLogger(__name__)

ai = Blueprint('ai')

@ai.route('/healthy/<tag>')
async def health(request, tag):
    return text("healthy is ok"+tag)

"""
curl 测试命令:

# 基础测试 - 本地环境
curl -X POST http://localhost/api/v1/ai-monitor/check \
  -H "Content-Type: application/json" \
  -d '{
    "create_time": "2024-01-01 12:00:00",
    "fingerprint": "test123"
  }'
"""

@ai.route('/check', methods=["POST"])
async def check(request):
    """
    AI盯盘检查接口 - 保持简洁，主要逻辑在monitor/entry.py中

    请求参数:
        create_time (str): 告警创建时间，格式: "YYYY-MM-DD HH:MM:SS"
        fingerprint (str): 告警指纹，唯一标识

    响应格式:
        {
            "data": "firing|suppressed",
            "message": "处理结果描述",
            "success": true|false
        }

    处理流程:
        1. 参数验证
        2. 查询告警信息
        3. 业务强依赖检查
        4. 时间阈值兜底逻辑检查
        5. 告警类型判断
        6. AI多模态分析
        7. 返回处理结果
    """
    try:
        # 1. 参数验证
        create_time, fingerprint, error_msg = validate_request_params(request.json)
        if error_msg:
            return json(format_response("firing", error_msg, False))

        # 2. 执行告警处理逻辑
        data_status, message, success = await process_alert_check(create_time, fingerprint)

        # 3. 返回结果
        return json(format_response(data_status, message, success))

    except Exception as err:
        logger.error(f"处理告警检查请求时发生异常: {err}")
        return json(format_response("firing", "未知错误无法提供 AI 盯盘能力", False))

"""
任务列表查询 curl 测试命令:

# 查询最近100个分析任务
curl -X GET http://localhost/api/v1/ai-monitor/task/list

# 查询特定任务详情
curl -X GET http://localhost/api/v1/ai-monitor/task/2024-01-01%2012:00:00/test123

# 查询任务步骤详情
curl -X GET http://localhost/api/v1/ai-monitor/task/step/2024-01-01%2012:00:00/test123

# K8s 环境测试
curl -X GET http://<node-ip>:30320/task/list
curl -X GET http://<node-ip>:30320/task/2024-01-01%2012:00:00/test123
curl -X GET http://<node-ip>:30320/task/step/2024-01-01%2012:00:00/test123
"""

@ai.route('/task/list', methods=["GET"])
async def get_task_list(request):
    """
    查询最近100个分析任务列表

    响应格式:
        {
            "data": [
                {
                    "create_time": "2024-01-01 12:00:00",
                    "fingerprint": "abc123",
                    "status": "suppressed",
                    "alertname": "CC-SRE TLB PSM 可用率",
                    "psm": "example.service",
                    "level": "warning",
                    "title": "告警标题",
                    "group": "sre",
                    "content": "告警内容",
                    "reason": "AI分析判断"
                }
            ],
            "message": "查询任务列表成功",
            "success": true
        }
    """
    try:
        from .handler.monitor.utils import get_task_list
        task_list = await get_task_list()

        if task_list:
            # 提取所有的 create_time 和 fingerprint 用于批量查询告警状态
            task_keys = [(task[0], task[1]) for task in task_list]  # (create_time, fingerprint)

            # 批量查询告警状态
            from .handler.monitor.utils import get_alarm_status_batch_by_keys
            alarm_status_map = await get_alarm_status_batch_by_keys(task_keys)

            # 格式化任务列表
            formatted_tasks = []
            for task in task_list:
                create_time, fingerprint, status, alertname, alert_source, psm, level, title, group, content, reason = task

                # 从批量查询结果中获取告警状态
                task_key = (create_time, fingerprint)
                alarm_status = alarm_status_map.get(task_key, "unknown")

                formatted_tasks.append({
                    "create_time": create_time.strftime('%Y-%m-%d %H:%M:%S') if create_time else None,
                    "fingerprint": fingerprint,
                    "ai_status": status,  # AI盯盘状态
                    "alarm_status": alarm_status,  # 从alertman_history查询的真实告警状态
                    "alertname": alertname,
                    "alert_source": alert_source,
                    "psm": psm,
                    "level": level,
                    "title": title,
                    "group": group,
                    "content": content,
                    "reason": reason
                })

            return json(format_response(formatted_tasks, "查询任务列表成功", success=True))
        else:
            return json(format_response([], "暂无任务记录", success=True))

    except Exception as e:
        logger.error(f"查询任务列表失败: {e}")
        return json(format_response([], f"查询失败: {str(e)}", success=False))

@ai.route('/task/<create_time>/<fingerprint>', methods=["GET"])
async def get_task_detail(request, create_time, fingerprint):
    """
    获取某个任务的详细信息

    路径参数:
        create_time: 告警创建时间 (URL编码)
        fingerprint: 告警指纹

    响应格式:
        {
            "data": {
                "task_info": {
                    "create_time": "2024-01-01 12:00:00",
                    "fingerprint": "abc123",
                    "status": "suppressed",
                    "alertname": "CC-SRE TLB PSM 可用率",
                    "psm": "example.service",
                    "level": "warning",
                    "title": "告警标题",
                    "group": "sre",
                    "content": "告警内容"
                },
                "steps": [
                    {
                        "step_id": 1,
                        "content": "base64编码",
                        "response": "ai 分析结果",
                        "create_at": "2024-01-01 12:00:05",
                    }
                ]
            },
            "message": "查询任务详情成功",
            "success": true
        }
    """
    try:
        # URL解码时间参数
        import urllib.parse
        decoded_create_time = urllib.parse.unquote(create_time)

        from .handler.monitor.utils import get_task_detail
        task_detail = await get_task_detail(decoded_create_time, fingerprint)

        if task_detail:
            return json(format_response(task_detail, "查询任务详情成功", success=True))
        else:
            return json(format_response(None, "未找到任务详情", success=False))

    except Exception as e:
        logger.error(f"查询任务详情失败: {e}")
        return json(format_response(None, f"查询失败: {str(e)}", success=False))

@ai.route('/ai_block_rules/list', methods=['GET'])
async def get_ai_block_rules(request):
    """
    获取AI阻断规则列表

    查询参数:
        alert_group: 可选，告警组筛选条件

    响应格式:
        {
            "data": [
                {
                    "rule_id": "uuid",
                    "block_type": "title|group|tg_fingerprint",
                    "content": "阻断内容",
                    "enable": true,
                    "update_at": "2024-01-01 12:00:00",
                    "alert_group": "商业化"
                }
            ],
            "message": "查询告警组 '商业化' 的AI阻断规则成功，共 X 条",
            "success": true
        }
    """
    try:
        # 获取查询参数
        alert_group = request.args.get('alert_group')

        from .handler.monitor.utils import get_ai_block_rules_list
        rules = await get_ai_block_rules_list(alert_group=alert_group)

        # 构建响应消息
        if alert_group:
            message = f"查询告警组 '{alert_group}' 的AI阻断规则成功，共 {len(rules)} 条"
        else:
            message = f"查询AI阻断规则成功，共 {len(rules)} 条"

        return json(format_response(rules, message, success=True))

    except Exception as e:
        logger.error(f"获取AI阻断规则列表失败: {e}")
        return json(format_response([], f"查询失败: {str(e)}", success=False))


@ai.route('/ai_block_rules/<rule_id>', methods=['POST'])
async def save_ai_block_rule(request, rule_id):
    """
    保存AI阻断规则

    请求参数:
        {
            "block_type": "title|group|tg_fingerprint",
            "content": "阻断内容",
            "enable": true
        }

    响应格式:
        {
            "data": {
                "rule_id": "uuid"
            },
            "message": "保存成功",
            "success": true
        }
    """
    try:
        data = request.json

        # 参数验证
        if not data:
            return json(format_response(None, "请求体不能为空", success=False))

        block_type = data.get('block_type', '')
        content = data.get('content', '')
        enable = data.get('enable', True)
        alert_group = data.get('alert_group', '')

        # 验证必需参数
        if not block_type:
            return json(format_response(None, "block_type不能为空", success=False))

        if block_type not in ['title', 'group', 'tg_fingerprint']:
            return json(format_response(None, "block_type必须是title、group或tg_fingerprint之一", success=False))

        if not content:
            return json(format_response(None, "content不能为空", success=False))

        # 保存规则
        from .handler.monitor.utils import save_ai_block_rule
        try:
            saved_rule_id = await save_ai_block_rule(rule_id, block_type, content, enable, alert_group)
            return json(format_response({"rule_id": saved_rule_id}, "保存AI阻断规则成功", success=True))
        except ValueError as ve:
            # 处理规则已存在的情况
            if "规则已存在" in str(ve):
                return json(format_response(None, "规则已存在，创建失败", success=False))
            else:
                raise ve

    except Exception as e:
        logger.error(f"保存AI阻断规则失败: {e}")
        return json(format_response(None, f"保存失败: {str(e)}", success=False))
