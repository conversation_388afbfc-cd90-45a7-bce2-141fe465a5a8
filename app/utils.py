from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any
import pytz
import asyncio
from functools import wraps
import logging

import aiomysql

from .config import base


logger = logging.getLogger(__name__)
beijing_tz = pytz.timezone('Asia/Shanghai')

def get_region():
    if base["REGION"] == "cn":
        return "cn"
    return "sg"


async def push_ai_result_with_picture(create_time: str, fingerprint: str, base64_image: str, ai_result: str):
    """
    保存结果的同时推送数据到lark_robot接口

    Args:
        create_time: 创建时间
        fingerprint: 指纹
        base64_image: base64图片数据
        ai_result: AI分析结果描述
    """
    import aiohttp
    import logging
    from .config import base

    logger = logging.getLogger(__name__)

    try:
        # 1. 检查是否已经创建过记录
        from .handler.monitor.utils import check_ai_result_exists
        is_new = not await check_ai_result_exists(create_time, fingerprint)

        logger.info(f"推送AI结果: {fingerprint}, is_new={is_new}")

        # 2. 构造请求数据
        request_data = {
            "create_time": create_time,
            "fingerprint": fingerprint,
            "picture": base64_image,
            "is_new": is_new,
            "des": ai_result
        }

        # 3. 获取服务器地址
        api_url = f"{base['BASE_URL']}/api/lark_robot/upload_picture"

        # 4. 发起HTTP请求
        timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时

        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(
                api_url,
                json=request_data,
                headers={"Content-Type": "application/json"}
            ) as response:

                if response.status == 200:
                    result = await response.json()
                    logger.info(f"推送AI结果成功: {fingerprint}, 响应: {result}")
                    return True, "推送成功"
                else:
                    error_text = await response.text()
                    logger.error(f"推送AI结果失败: {fingerprint}, 状态码: {response.status}, 响应: {error_text}, 请求报文: {request_data}")
                    return False, f"推送失败: HTTP {response.status}"

    except aiohttp.ClientError as e:
        logger.error(f"推送AI结果网络错误: {fingerprint}, 错误: {e}")
        return False, f"网络错误: {str(e)}"
    except Exception as e:
        logger.error(f"推送AI结果异常: {fingerprint}, 错误: {e}")
        return False, f"推送异常: {str(e)}"

def get_beijing_time(_datetime):
    return _datetime.astimezone(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')

def storage(_type):
    def decorate(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            db = None
            cursor = None
            try:
                if _type == "cmdb":
                    db = await aiomysql.connect(
                            host=base["SR_HOST"],
                            user=base["DB_USER"],
                            port=9030,
                            charset='utf8',
                            password=base["DB_PASSWD"],
                            db='cmdb',
                      
                        )
                elif _type == "monitor":
                    db = await aiomysql.connect(
                            host=base["SR_HOST"],
                            user=base["DB_USER"],
                            port=9030,
                            charset='utf8',
                            password=base["DB_PASSWD"],
                            db='monitor',
                        )
                else:
                    raise ValueError(f"Unsupported database type: {_type}")

                cursor = await db.cursor()
                res = await func(db, cursor, *args, **kwargs)
                return res
            finally:
                if cursor:
                    await cursor.close()
                if db:
                    db.close()
        return wrapper
    return decorate

def format_response(data_status: str, message: str, success: bool = True) -> Dict[str, Any]:
    """
    格式化响应数据
    
    Args:
        data_status: 数据状态
        message: 消息
        success: 是否成功
        
    Returns:
        Dict[str, Any]: 格式化的响应数据
    """
    return {
        "data": data_status,
        "message": message,
        "success": success,
    }
