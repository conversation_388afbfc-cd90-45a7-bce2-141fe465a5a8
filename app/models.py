
from tortoise.models import Model
from tortoise import fields

# class TLBAvailableRule(Model):
#     id = fields.IntField(pk=True)
#     level = fields.Char<PERSON><PERSON>(128, null=False)
#     cluster = fields.Char<PERSON><PERSON>(128, null=False)
#     domain = fields.Char<PERSON><PERSON>(128, null=False)
#     route = fields.Char<PERSON>ield(128, null=False)
#     service = fields.CharField(128, null=False)
#     threshold = fields.FloatField()
#     owner = fields.CharField(128, null=False)
#     enable = fields.IntField()
#     desc = fields.TextField()
#     path = fields.Char<PERSON>ield(128, index=True, default="")

#     class Meta:
#         table = "tlb_available_rule"
#         unique_together = (('level', 'cluster', 'domain', 'route', 'service'),)
