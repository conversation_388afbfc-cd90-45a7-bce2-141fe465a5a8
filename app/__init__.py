import traceback
import logging

from sanic import Sanic
from sanic.response import text, json
from tortoise.contrib.sanic import register_tortoise
import httpx
from sanic import Blueprint
from sanic.exceptions import MethodNotSupported
from .config import base
from .tasks import task_list
from .views import ai

# 设置全局日志等级为 INFO
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    force=True  # 强制重新配置日志
)

# 设置关键模块的日志等级
key_modules = [
    'app.handler.monitor.utils',
    'app.handler.monitor.handler',
    'app.handler.monitor.entry',
    'app.utils',
    'app.tasks'
]

for module in key_modules:
    logging.getLogger(module).setLevel(logging.INFO)

# 设置第三方库的日志等级
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('aiohttp').setLevel(logging.WARNING)

app = Sanic(__name__)
def create_app():
    app.blueprint(ai, url_prefix="/api/v1/ai-monitor")
    app.config.update(base)
    # register_tortoise(
    #     app, db_url=app.config["DB"], modules={"models": ["app.models"]}, generate_schemas=True
    # )

    for task in task_list:
        app.add_task(task)

    return app
