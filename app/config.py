import os
import uuid
import socket

base = {
    "DEBUG": False,
    "SECRET_KEY": uuid.uuid4().hex,
    # "DB": os.getenv("DB"),

    # AI模型配置
    "MODEL_API_KEY": os.getenv("MODEL_API_KEY"),

    # 告警处理配置
    "ALERT_DURATION_THRESHOLD_MINUTES": int(os.getenv("ALERT_DURATION_THRESHOLD_MINUTES", "20")),  # 告警持续时间阈值（分钟）

    "DB_USER": os.getenv("DB_USER"),
    "DB_PASSWD": os.getenv("DB_PASSWD"),
    "SR_HOST": os.getenv("SR_HOST"),
    
    # 图表生成配置
    "AUTH_URL": os.getenv("AUTH_URL", ""),
    "JWT_TOKEN": os.getenv("JWT_TOKEN", ""),
    "BASE_URL": os.getenv("BASE_URL", ""),
    "REGION": os.getenv("REGION", ""),
    "GRAFANA_OPEN_API": os.getenv("GRAFANA_OPEN_API", ""),
}