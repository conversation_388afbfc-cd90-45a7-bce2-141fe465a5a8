#!/usr/bin/env python3
"""
调试推送功能问题
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置详细的日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def debug_push_step_by_step():
    """逐步调试推送功能"""
    print("🔍 逐步调试推送功能")
    
    # 1. 检查配置
    print("\n1️⃣ 检查配置")
    try:
        from app.config import base
        print(f"   BASE_URL: {base.get('BASE_URL', '未设置')}")
        
        if not base.get('BASE_URL'):
            print("   ❌ BASE_URL未设置，这可能是问题所在")
            print("   💡 请设置环境变量: export BASE_URL=http://your-server:port")
            return False
        else:
            api_url = f"{base['BASE_URL']}/api/lark_robot/upload_picture"
            print(f"   ✅ 推送URL: {api_url}")
    except Exception as e:
        print(f"   ❌ 配置检查失败: {e}")
        return False
    
    # 2. 检查数据库连接
    print("\n2️⃣ 检查数据库连接")
    try:
        from app.handler.monitor.utils import check_ai_result_exists
        
        test_time = "2024-01-01 12:00:00"
        test_fp = "test_debug"
        
        exists = await check_ai_result_exists(test_time, test_fp)
        print(f"   ✅ 数据库连接正常，测试查询结果: {exists}")
    except Exception as e:
        print(f"   ❌ 数据库连接失败: {e}")
        return False
    
    # 3. 测试推送函数（模拟数据）
    print("\n3️⃣ 测试推送函数")
    try:
        from app.utils import push_ai_result_with_picture
        
        test_data = {
            'create_time': '2024-01-01 12:00:00',
            'fingerprint': f'debug_test_{int(datetime.now().timestamp())}',
            'base64_image': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA4nEKtAAAAABJRU5ErkJggg==',
            'ai_result': '调试测试结果'
        }
        
        print(f"   测试数据: {test_data['fingerprint']}")
        
        # 启用详细日志
        logger = logging.getLogger('app.utils')
        logger.setLevel(logging.DEBUG)
        
        success, message = await push_ai_result_with_picture(**test_data)
        
        print(f"   推送结果: success={success}, message={message}")
        
        if not success:
            print(f"   ❌ 推送失败，检查日志中的详细错误信息")
        else:
            print(f"   ✅ 推送成功")
            
        return success
        
    except Exception as e:
        print(f"   ❌ 推送函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def debug_ai_analysis_flow():
    """调试AI分析流程"""
    print("\n🔄 调试AI分析流程")
    
    try:
        # 1. 获取告警数据
        print("\n1️⃣ 获取告警数据")
        from app.tasks import get_recent_alerts
        
        alerts = await get_recent_alerts(minutes=60)
        print(f"   查询到 {len(alerts)} 个告警")
        
        if not alerts:
            print("   ⚠️  没有告警数据，创建模拟数据")
            alert_data = {
                'psm': 'debug.test.service',
                'title': '调试测试告警',
                'level': 'critical',
                'content': '{"startsAt": "2024-01-01T12:00:00Z"}',
                'group': 'debug-group'
            }
            create_time = "2024-01-01 12:00:00"
            fingerprint = f"debug_ai_{int(datetime.now().timestamp())}"
        else:
            test_alert = alerts[0]
            alert_data = {
                'psm': test_alert['psm'],
                'title': test_alert['title'],
                'level': test_alert['level'],
                'content': test_alert['content'],
                'group': test_alert.get('group', 'unknown')
            }
            create_time = test_alert['create_time']
            fingerprint = f"debug_ai_{int(datetime.now().timestamp())}"
        
        print(f"   使用告警: {fingerprint}")
        print(f"   标题: {alert_data['title']}")
        
        # 2. 测试AI分析
        print("\n2️⃣ 测试AI分析")
        from app.handler.monitor.handler import common
        
        # 启用详细日志
        logger = logging.getLogger('app.handler.monitor.handler')
        logger.setLevel(logging.DEBUG)
        
        print(f"   调用common处理器...")
        status, message, reason = await common(alert_data, create_time, fingerprint)
        
        print(f"   AI分析结果:")
        print(f"     状态: {status}")
        print(f"     消息: {message}")
        print(f"     原因: {reason}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ AI分析流程调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_log_output():
    """检查日志输出设置"""
    print("\n📋 检查日志输出设置")
    
    # 检查各个关键模块的日志级别
    modules = [
        'app.utils',
        'app.handler.monitor.handler', 
        'app.tasks',
        'aiohttp.client'
    ]
    
    print(f"   关键模块日志级别:")
    for module in modules:
        logger = logging.getLogger(module)
        level = logging.getLevelName(logger.getEffectiveLevel())
        print(f"     {module}: {level}")
    
    # 设置所有相关模块为DEBUG级别
    print(f"\n   设置详细日志级别...")
    for module in modules:
        logging.getLogger(module).setLevel(logging.DEBUG)
    
    print(f"   ✅ 已设置详细日志级别")

def print_debug_summary():
    """打印调试总结"""
    print("\n📋 调试问题总结:")
    print()
    
    print("🔍 可能的问题原因:")
    print("   1. BASE_URL环境变量未设置")
    print("   2. lark_robot服务未启动或不可访问")
    print("   3. 网络连接问题")
    print("   4. AI分析流程未正确触发推送")
    print("   5. 日志级别设置过高，看不到详细信息")
    print()
    
    print("🔧 解决方案:")
    print("   1. 设置环境变量: export BASE_URL=http://your-server:port")
    print("   2. 确保lark_robot服务正常运行")
    print("   3. 检查防火墙和网络配置")
    print("   4. 查看详细的错误日志")
    print("   5. 使用DEBUG日志级别")
    print()
    
    print("📊 验证步骤:")
    print("   1. 运行此调试脚本查看详细信息")
    print("   2. 手动测试API接口: curl -X POST $BASE_URL/api/lark_robot/upload_picture")
    print("   3. 检查后台任务是否正常运行")
    print("   4. 查看数据库中的记录是否正确保存")

async def main():
    """主函数"""
    print("=" * 70)
    print("🔍 AI 盯盘系统 - 推送功能调试")
    print("=" * 70)
    
    # 检查日志输出设置
    check_log_output()
    
    # 逐步调试推送功能
    push_ok = await debug_push_step_by_step()
    
    # 调试AI分析流程
    ai_ok = await debug_ai_analysis_flow()
    
    # 打印调试总结
    print_debug_summary()
    
    # 总结结果
    print("\n" + "=" * 70)
    print("📊 调试结果:")
    print(f"   推送功能: {'✅ 正常' if push_ok else '❌ 异常'}")
    print(f"   AI分析流程: {'✅ 正常' if ai_ok else '❌ 异常'}")
    
    if not push_ok:
        print("\n💡 推送功能问题，请检查:")
        print("   • BASE_URL环境变量")
        print("   • lark_robot服务状态")
        print("   • 网络连接")
    
    if not ai_ok:
        print("\n💡 AI分析流程问题，请检查:")
        print("   • 数据库连接")
        print("   • AI分析配置")
        print("   • 后台任务运行状态")
    
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())
