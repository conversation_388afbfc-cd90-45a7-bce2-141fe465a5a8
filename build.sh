#!/bin/bash
dir_name=ai-monitors
new_number=$(date +%Y%m%d%H%M%S)
current_branch=$(git symbolic-ref --short HEAD)
if [ "$current_branch" != "master" ]; then
    echo "当前不在master分支"
    exit 1
fi
git pull 

local_changes=$(git status --porcelain)
if [ -n "$local_changes" ]; then
    echo "存在未提交的本地变更"
    exit 1
fi

local_status=$(git status)
if [[ $local_status == *"Your branch is ahead of"* ]]; then
    echo "存在未提交到远端仓库的commit"
    exit 1
else
    echo "不存在未提交到远端仓库的commit"
fi



if [ "$1" == "cn" ]; then
    workflow_name="quicksight.bytedance.net/${dir_name}"
elif [ "$1" == "i18n" ]; then
    workflow_name="quicksight-i18n.byteintl.net/${dir_name}"
else
    echo "请指定区域，cn 或 i18n"
    exit 1
fi 



image_name="${workflow_name}:v0.0.${new_number}"
docker build -t $image_name . && docker push $image_name